<?php

use Spatie\LaravelSettings\Migrations\SettingsMigration;

return new class extends SettingsMigration
{
    public function up(): void
    {
        $this->migrator->add('landing.about_description', [
            'active' => 0,
            'ar' => 'تطبيق هومي هو وجهتك الأمثل لطلب خدمات الصيانة مثل الكهرباء , السباك , النجارة , الستالايت , التكييف , مكافحة الحشرات وغسيل السجاد . نقدم في هومي تجربة مستخدم متميزة تساعدك على الطلب في أسرع وقت وبالخيارات التي تناسبك .',
            'en' => 'The Homi application is your ideal destination for requesting maintenance services such as electricity, plumbing, carpentry, satellite, air conditioning, pest control, and carpet washing. At Homi, we provide an excellent user experience that helps you order as quickly as possible and with the options that suit you.',
        ]);
        $this->migrator->add('landing.about_features', [
            [
                'title' => [
                    'ar' => 'واجهة مستخدم تمكنك من الطلب في ثواني',
                    'en' => 'A user interface that enables you to order in seconds',
                ],
                'icon' => 'stopwatch',
            ],
            [
                'title' => [
                    'ar' => 'مجموعة احترافية من مقدمي الخدمات',
                    'en' => 'Professional group of service providers',
                ],
                'icon' => 'helmet-safety',
            ],
            [
                'title' => [
                    'ar' => 'خيارات دفع إلكترونية آمنة',
                    'en' => 'Secure electronic payment options',
                ],
                'icon' => 'credit-card',
            ],
            [
                'title' => [
                    'ar' => 'إشعارات تفاعلية لمتابعة حالة الحجز',
                    'en' => 'Interactive notifications to follow the reservation status',
                ],
                'icon' => 'bell',
            ],
        ]);

        $this->migrator->add('landing.our_features_description', [
            'active' => 0,
            'ar' => 'مع تجربة المستخدم الأسهل , أطلب خدمات الصيانة في ثواني',
            'en' => 'With an easier user experience, request maintenance services in seconds',
        ]);
        $this->migrator->add('landing.feature_image', '');
        $this->migrator->add('landing.features', [
            [
                'title' => [
                    'ar' => 'خدمات احترافية بجودة عالية',
                    'en' => 'Professional services with high quality',
                ],
                'icon' => 'star',
            ],
            [
                'title' => [
                    'ar' => 'تحديد موعد الزيارة اللي يناسبك',
                    'en' => 'Determine the visit time that suits you',
                ],
                'icon' => 'calendar-clock',
            ],
            [
                'title' => [
                    'ar' => 'اختيار الفني المناسب',
                    'en' => 'Choose the appropriate technician',
                ],
                'icon' => 'helmet-safety',
            ],
            [
                'title' => [
                    'ar' => 'اسعار خدماتنا تنافسية ومناسبة للجميع',
                    'en' => 'The prices of our services are competitive and suitable for everyone',
                ],
                'icon' => 'money-bill-simple-wave',
            ],
            [
                'title' => [
                    'ar' => 'طرق دفع متعددة وآمنة',
                    'en' => 'Multiple and secure payment methods',
                ],
                'icon' => 'credit-card',
            ],
            [
                'title' => [
                    'ar' => 'سهولة التواصل مع خدمة العملاء',
                    'en' => 'Ease of communication with customer service',
                ],
                'icon' => 'user-headset',
            ],
        ]);

        $this->migrator->add('landing.app_screen', []);
    }
};