<?php

namespace Tasawk\Api\V1\Manager;

use Tasawk\Api\Facade\Api;
use Tasawk\Enum\BalanceRequestStatusEnum;
use Tasawk\Enum\OrderStatus;
use Tasawk\Enum\ReceiptMethods;
use Tasawk\Http\Requests\Api\Manager\RequestBalanceRequest;
use Tasawk\Http\Resources\Api\Manager\Orders\RateResource;
use Tasawk\Http\Resources\Api\Manager\Orders\WalletWithdrawRequestResource;
use Tasawk\Models\BalanceRequest;
use Tasawk\Models\OrderRate;
use Tasawk\Models\Worker;


class HomeService {
    public function statistics() {

        return Api::isOk("statistics", [

            'completed_orders_total' => auth()->user()->actAs(Worker::class)->orders()->where('status', OrderStatus::COMPLETED)->count(),
            'wallet_balance' => auth()->user()->balance,

        ]);
    }

    public function rates() {
        $rates = OrderRate::whereHas("order", fn($q) => $q->where('worker_id', auth()->id()))->latest()->paginate();
        return Api::isOk("rates", RateResource::collection($rates));
    }

    public function requests() {
        return Api::isOk("requests", WalletWithdrawRequestResource::collection(BalanceRequest::where('user_id', auth()->id())->latest()->paginate()));
}
    public function requestWalletBalance(RequestBalanceRequest $request) {
        BalanceRequest::create([
            'user_id' => auth()->id(),
            'status' => BalanceRequestStatusEnum::PENDING,
            'amount' => $request->amount
        ]);
        return Api::isOk("request sent");
    }
}
