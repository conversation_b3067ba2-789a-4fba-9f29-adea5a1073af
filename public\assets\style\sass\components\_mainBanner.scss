.mainBanner {
  height: 100vh;
  overflow: hidden;
  .container {
    height: 100%;
    position: relative;
  }
}

.hero-mobile {
  position: absolute;
  bottom: 30px;
  z-index: 2;
  inset-inline-start: 0;
  width: 850px;
}
.mainBanner-parent {
  direction: rtl;
  background-image: url(../images/hero-bg.jpg);
  background-position: center 100%;
  background-size: cover;
  background-color: $green-color;
  height: 100%;
  position: relative;
  clip-path: ellipse(100% 100% at 50% 0%);
}

.hero-pattern {
  position: absolute;
  bottom: 7%;
  inset-inline-start: -2%;
  z-index: 1;
  width: 942px;
}
.banner-desc {
  width: 470px;
  text-align: center;
  position: absolute;
  z-index: 3;
  top: 50%;
  inset-inline-end: 50px;
  transform: translateY(-50%);
  .main-title {
    font-size: 36px;
    text-transform: capitalize;
    line-height: 50px;
    margin-bottom: 10px;
    color: #ffffff;
    font-weight: 700;
    text-align: start;
  }
  .second-title {
    font-size: 28px;
    line-height: 40px;
    color: #ffffff;
    font-weight: 400;
    text-align: start;
    margin: 0;
  }
  .download-links {
    justify-content: flex-start;
    a {
      transition: background-color ease-in-out 0.2s;
    }
    a:hover {
      background-color: $orange-color;
    }
  }
}

// scroll animated

.scroll-btn {
  position: absolute;
  display: flex;
  width: 100%;
  z-index: 4;
  align-items: center;
  justify-content: center;
  bottom: 45px;
  text-align: center;
  span {
    width: 24px;
    height: 45px;
    border-radius: 12px;
    background-color: rgba(2, 119, 250, 0);
    border: 2px solid #ffffff;
    position: relative;
    display: inline-block;
    transition: all ease 0.3s;
  }
}

.scroll-btn span::after {
  content: "";
  width: 2px;
  height: 10px;
  background-color: #ffffff;
  top: 6px;
  opacity: 0;
  left: 48%;
  z-index: 3;
  position: absolute;
  transform: translateX(-48%);
  animation-name: scroll;
  animation-duration: 2s;
  animation-iteration-count: infinite;
}
@keyframes scroll {
  0% {
    opacity: 1;
  }
  100% {
    transform: translateY(20px);
  }
}

@media screen and (max-width: 1700px) {
  .hero-mobile {
    width: 580px;
    bottom: 0;
  }
  .hero-pattern {
    width: 700px;
    bottom: -2%;
    inset-inline-start: -4%;
    z-index: 0;
  }
}

@include max-1200 {
  .hero-mobile {
    width: 520px;
  }
  .hero-pattern {
    width: 600px;
    bottom: 0;
    inset-inline-start: -8%;
    z-index: 0;
  }
}

@include max-992 {
  .hero-pattern {
    width: 500px;
    bottom: 6%;
    inset-inline-start: -14%;
    z-index: 0;
  }
  .hero-mobile {
    bottom: 18px;
    /* inset-inline-end: 0; */
    width: 400px;
  }
  .banner-desc {
    width: 375px;
    inset-inline-end: 0;
  }
}

@include max-768 {
  .hero-pattern {
    width: 400px;
    bottom: 6%;
    inset-inline-start: -3%;
    z-index: 0;
  }
  .mainBanner .container {
    overflow: auto;
  }
  .banner-desc .download-links {
    width: 100%;
    justify-content: center;
  }
  .banner-desc .download-links a {
    width: 100%;
    max-width: 185px;
  }
  .hero-mobile {
    bottom: -3px;
    inset-inline-end: -70px;
    width: 420px;
  }
  .scroll-btn span {
    border-color: #000;
    &::after {
      background-color: #000;
    }
  }

  .scroll-btn {
    bottom: 54px;
  }
  .banner-desc {
    position: static;
    inset-inline-start: unset;
    transform: unset;
    margin-top: 160px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 100%;
    margin-left: auto;
    margin-right: auto;
    max-width: none;
  }
  .download-links {
    margin-top: 30px;
  }

  .banner-desc .main-title {
    font-size: 27px;
    text-align: center;
    width: 100%;
    line-height: 1.5;
    margin-bottom: 12px;
  }
  .mainBanner-parent {
    clip-path: ellipse(160% 100% at 50% 0%);
  }
  .banner-desc .second-title {
    font-size: 21px;
    line-height: 1.5;
    text-align: center;
  }
  .banner-desc .download-links a .google,
  .banner-desc .download-links a .apple {
    height: 26px;
    width: auto;
  }
  .download-links a {
    height: 52px;
  }
}

@media screen and (max-width: 360px) {
  .hero-mobile {
    bottom: -10px;
    inset-inline-end: -48px;
    width: 350px;
  }
}
