<?xml version="1.0" encoding="utf-8"?>
<!-- Generator: Adobe Illustrator 28.0.0, SVG Export Plug-In . SVG Version: 6.00 Build 0)  -->
<svg version="1.1" id="Layer_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px"
	 viewBox="0 0 172.1 38.3" style="enable-background:new 0 0 172.1 38.3;" xml:space="preserve">
<style type="text/css">
	.st0{fill:url(#SVGID_1_);}
	.st1{fill:url(#SVGID_00000121246580816979071160000018257085951069908637_);}
	.st2{fill:url(#SVGID_00000118367403824296966870000012658148933289723051_);}
	.st3{fill:url(#SVGID_00000140705097009157488560000016573212539654103726_);}
	.st4{fill:#FFFFFF;}
</style>
<g id="Layer_1-2">
	
		<linearGradient id="SVGID_1_" gradientUnits="userSpaceOnUse" x1="-269.405" y1="353.3821" x2="-294.365" y2="378.3421" gradientTransform="matrix(1 0 0 1 287 -351.05)">
		<stop  offset="0" style="stop-color:#00A0FF"/>
		<stop  offset="1.000000e-02" style="stop-color:#00A1FF"/>
		<stop  offset="0.26" style="stop-color:#00BEFF"/>
		<stop  offset="0.51" style="stop-color:#00D2FF"/>
		<stop  offset="0.76" style="stop-color:#00DFFF"/>
		<stop  offset="1" style="stop-color:#00E3FF"/>
	</linearGradient>
	<path class="st0" d="M0.7,0.6C0.2,1.2,0,1.9,0,2.7v32.9c0,0.8,0.2,1.5,0.7,2.1l0.1,0.1l18.4-18.4v-0.4L0.8,0.5L0.7,0.6z"/>
	
		<linearGradient id="SVGID_00000182516299561677359860000003139627470968371382_" gradientUnits="userSpaceOnUse" x1="-251.51" y1="370.1771" x2="-287.5" y2="370.1771" gradientTransform="matrix(1 0 0 1 287 -351.05)">
		<stop  offset="0" style="stop-color:#FFE000"/>
		<stop  offset="0.41" style="stop-color:#FFBD00"/>
		<stop  offset="0.78" style="stop-color:#FFA500"/>
		<stop  offset="1" style="stop-color:#FF9C00"/>
	</linearGradient>
	<path style="fill:url(#SVGID_00000182516299561677359860000003139627470968371382_);" d="M25.4,25.5l-6.1-6.1v-0.4l6.1-6.1l0.1,0.1
		l7.3,4.2c2.1,1.2,2.1,3.1,0,4.3l-7.3,4.1L25.4,25.5z"/>
	
		<linearGradient id="SVGID_00000031890987011557760160000003059446228810104245_" gradientUnits="userSpaceOnUse" x1="-264.9025" y1="373.5846" x2="-298.7625" y2="407.4346" gradientTransform="matrix(1 0 0 1 287 -351.05)">
		<stop  offset="0" style="stop-color:#FF3A44"/>
		<stop  offset="1" style="stop-color:#C31162"/>
	</linearGradient>
	<path style="fill:url(#SVGID_00000031890987011557760160000003059446228810104245_);" d="M25.5,25.4l-6.3-6.3L0.7,37.7
		c0.7,0.7,1.8,0.8,3.1,0.1L25.5,25.4z"/>
	
		<linearGradient id="SVGID_00000010306462199162248250000014093769554227958198_" gradientUnits="userSpaceOnUse" x1="-290.98" y1="340.6871" x2="-275.86" y2="355.8071" gradientTransform="matrix(1 0 0 1 287 -351.05)">
		<stop  offset="0" style="stop-color:#32A071"/>
		<stop  offset="7.000000e-02" style="stop-color:#2DA771"/>
		<stop  offset="0.48" style="stop-color:#15CF74"/>
		<stop  offset="0.8" style="stop-color:#06E775"/>
		<stop  offset="1" style="stop-color:#00F076"/>
	</linearGradient>
	<path style="fill:url(#SVGID_00000010306462199162248250000014093769554227958198_);" d="M25.5,12.8L3.8,0.5
		C2.5-0.2,1.4-0.1,0.7,0.6l18.5,18.5L25.5,12.8z"/>
</g>
<path class="st4" d="M86.3,13.5c-3.5,0-6.2,2.9-6.2,6.3s2.9,6.2,6.3,6.2c3.4,0,6.2-2.8,6.2-6.2c0-3.4-2.7-6.2-6.1-6.3
	C86.4,13.5,86.4,13.5,86.3,13.5z M86.3,23.5c-2,0-3.6-1.7-3.6-3.7c0,0,0,0,0-0.1c-0.1-2,1.4-3.6,3.4-3.8c2-0.1,3.6,1.4,3.8,3.4
	c0,0.1,0,0.3,0,0.4C90,21.8,88.4,23.5,86.3,23.5C86.4,23.6,86.3,23.6,86.3,23.5L86.3,23.5z M72.5,13.5c-3.5,0.1-6.2,2.9-6.1,6.3
	s2.9,6.2,6.3,6.1c3.4-0.1,6.1-2.8,6.2-6.2c0-3.4-2.7-6.2-6.1-6.3C72.6,13.5,72.5,13.5,72.5,13.5z M72.5,23.5c-2,0-3.6-1.7-3.6-3.7
	c0,0,0,0,0-0.1c-0.2-2,1.2-3.7,3.1-4c2-0.2,3.7,1.2,4,3.1c0,0.3,0,0.6,0,0.8C76.1,21.8,74.5,23.5,72.5,23.5
	C72.5,23.6,72.5,23.6,72.5,23.5L72.5,23.5z M56,15.4v2.7h6.4c-0.1,1.2-0.6,2.4-1.4,3.3c-1.3,1.3-3.1,2-5,1.9c-3.9,0-7-3.2-7-7
	s3.2-7,7-7c1.8,0,3.5,0.7,4.8,1.9l1.9-1.9c-1.8-1.7-4.2-2.7-6.7-2.7c-5.4-0.1-9.9,4.3-10,9.7c0.1,5.4,4.6,9.8,10,9.7
	c2.6,0.1,5.1-0.9,6.8-2.7c1.6-1.7,2.4-3.9,2.3-6.2c0-0.6-0.1-1.1-0.2-1.7H56z M123.3,17.5c-0.8-2.3-2.9-3.9-5.4-4
	c-3.3,0-6,2.8-5.9,6.1c0,0.1,0,0.1,0,0.2c-0.1,3.4,2.7,6.2,6.1,6.2c0.1,0,0.1,0,0.2,0c2.1,0,4.1-1,5.3-2.8l-2.2-1.4
	c-0.7,1.1-1.8,1.7-3.1,1.7c-1.3,0.1-2.5-0.7-3.1-1.9l8.5-3.5L123.3,17.5z M114.7,19.6c-0.1-1.9,1.3-3.5,3.2-3.6c0,0,0,0,0.1,0
	c1-0.1,1.9,0.4,2.3,1.3L114.7,19.6z M107.8,25.6h2.8V7.3h-2.8V25.6z M103.3,14.9L103.3,14.9c-1-0.9-2.2-1.4-3.4-1.4
	c-3.4,0.1-6.1,3-6,6.5c0.1,3.3,2.7,5.9,6,6c1.3,0,2.5-0.5,3.3-1.4h0.1v0.9c0,2.4-1.3,3.7-3.4,3.7c-1.4,0-2.7-0.9-3.2-2.2l-2.4,1
	c0.9,2.3,3.2,3.7,5.6,3.7c3.3,0,6-1.9,6-6.5V13.9h-2.6L103.3,14.9L103.3,14.9z M100.1,23.6c-2,0-3.6-1.7-3.5-3.7c0,0,0-0.1,0-0.1
	c-0.1-2,1.5-3.7,3.5-3.8c0,0,0,0,0.1,0c1.9,0.1,3.5,1.7,3.4,3.7c0,0,0,0.1,0,0.1c0.2,1.9-1.3,3.6-3.2,3.8
	C100.2,23.5,100.2,23.6,100.1,23.6L100.1,23.6z M136.3,7.3h-6.6v18.3h2.8v-7h3.9c3.2,0.1,6-2.4,6.1-5.6c0,0,0-0.1,0-0.1
	C142.4,9.7,139.7,7.2,136.3,7.3C136.4,7.3,136.4,7.3,136.3,7.3L136.3,7.3z M136.4,16.1h-3.9V9.8h3.9c1.7-0.1,3.2,1.2,3.3,3
	c0.1,1.7-1.2,3.2-3,3.3C136.7,16.1,136.5,16.1,136.4,16.1z M153.6,13.5c-2.1-0.1-4,1-4.9,2.8l2.5,1c0.9-1.4,2.8-1.8,4.1-0.9
	c0.7,0.4,1.1,1.1,1.3,1.9v0.2c-0.9-0.5-1.9-0.7-2.9-0.7c-2.6,0-5.4,1.4-5.4,4.1c0,2.5,2.2,4,4.6,4c1.4,0.1,2.8-0.6,3.5-1.8h0.1v1.4
	h2.7v-7C159.2,15.3,156.8,13.5,153.6,13.5L153.6,13.5z M153.2,23.6c-0.9,0-2.2-0.5-2.2-1.6c0-1.4,1.6-2,2.9-2c0.9,0,1.8,0.2,2.5,0.6
	C156.3,22.3,154.9,23.5,153.2,23.6L153.2,23.6z M169,13.9l-3.2,8h-0.1l-3.3-8h-3l4.9,11.1l-2.8,6.2h2.9l7.6-17.3H169z M144,25.6h2.8
	V7.3H144V25.6z"/>
</svg>
