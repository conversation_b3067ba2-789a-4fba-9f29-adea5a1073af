<?php

namespace Tasawk\Actions\Customer;

use Cknow\Money\Money;
use Darryldecode\Cart\CartCondition;
use GeometryLibrary\SphericalUtil;
use Lorisleiva\Actions\Concerns\AsAction;
use Tasawk\Enum\ReceiptMethods;
use Tasawk\Lib\Cart;
use Tasawk\Lib\Utils;
use Tasawk\Models\AddressBook;
use Tasawk\Models\Catalog\Product;
use Tasawk\Models\Order;


class BuildCartInstanceAction {
    use AsAction;

    protected $data = [];

    public function handle() {
        
        /**
         *
         * @var Cart $cart
         * */

        $cart = app('cart');
        $order = request()->route('order');
        $service = $order->service;
        $cart->applyItem($service->title, $service->price->formatByDecimal(),1, [],[]);
        foreach (request()->items as $_product) {
            $conditions = [];
            $attributes = [];
            $price = Money::parse($_product['price']*100)->formatByDecimal();
            $cart->applyItem($_product['name'], $price, $_product['quantity'], $attributes, $conditions);
        }


        $cart->applyTaxes();
        return $cart;
    }


}
