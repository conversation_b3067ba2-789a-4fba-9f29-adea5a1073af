<?php

namespace Tasawk\Http\Requests\Api\Manager\Profile;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;
use Tasawk\Rules\KSAPhoneRule;

class UpdateProfileRequest extends FormRequest {
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize() {
        return true;
    }

    protected function prepareForValidation() {

    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules() {
        $worker = auth()->user();

        $fleetAccount = $worker->fleetAccount;
        return [
            'logo' => [
                'nullable',
                'image',
                'mimes:jpeg,png,jpg,gif,svg',
                'max:2048'
            ],
            'name' => ['required', 'string', 'min:3'],
            'email' => ['required', 'email',Rule::unique('users', 'email')->ignore($worker->id)],
            'phone' => ['required', new KSAPhoneRule(),Rule::unique('users', 'phone')->ignore($worker->id)],
            'city_id' => ['required', 'exists:cities,id'],

            'location' => ['required', 'array'],
            'location.lat' => ['required', 'numeric'],
            'location.lng' => ['required', 'numeric'],
            'categories' => ['required', 'array'],
            'categories.*' => ['required', 'exists:categories,id'],
            'job_title' => [
                Rule::requiredIf(fn() => $fleetAccount->nationality == 'other')
                , 'string'],
            'bondsman_name' => [
                Rule::requiredIf(fn() => $fleetAccount->nationality == 'other')
                , 'string'],
            'bondsman_phone' => [
                Rule::requiredIf(fn() => $fleetAccount->nationality == 'other')
                , new KSAPhoneRule()
            ],
            'organization_name' => [
                Rule::requiredIf(fn() => $fleetAccount->residence_type == 'professional' && $fleetAccount->account_type != 'company' && $fleetAccount->nationality == 'other')
                , 'string'],
            'bank_account' => ['required', 'string'],
            'commercial_registration_number' => [
                Rule::requiredIf(fn() => $fleetAccount->account_type == 'company' || $fleetAccount->residence_type == 'professional'&& $fleetAccount->nationality == 'other')
                , 'string'],
            'commercial_registration_image' => [
                'nullable',
                'image',
                'mimes:jpeg,png,jpg,gif,svg',
                'max:2048'
            ],
            'residence_image' => [
               'nullable'
                , 'image', 'mimes:jpeg,png,jpg,gif,svg', 'max:2048'],
            'approve_letter_image' => [
                'nullable'
                , 'image', 'mimes:jpeg,png,jpg,gif,svg', 'max:2048'],


        ];
    }


}
