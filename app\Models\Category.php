<?php

namespace Tasawk\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;
use Spatie\Translatable\HasTranslations;
use Tasawk\Models\Catalog\Product;
use Tasawk\Traits\Publishable;

class Category extends Model implements HasMedia {
    use HasTranslations, Publishable, InteractsWithMedia;

    protected $fillable = ['name', 'status', 'parent_id','sort'];
    public $translatable = ['name'];
    use HasFactory;

    public function scopeParent($builder) {
        return $builder->where('parent_id', null);
    }

    public function scopeChildren($builder) {
        return $builder->where('parent_id', "!=", null);
    }

    public function children() {
        return $this->hasMany(Category::class, 'parent_id');
    }

    public function services(): \Illuminate\Database\Eloquent\Relations\HasMany {
        return $this->hasMany(Service::class);
    }


}
