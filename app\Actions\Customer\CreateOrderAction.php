<?php

namespace Tasawk\Actions\Customer;

use Illuminate\Support\Carbon;
use Lorisleiva\Actions\Concerns\AsAction;
use Str;
use Tasawk\Enum\OrderStatus;
use Tasawk\Lib\Utils;
use Tasawk\Models\Order;


class CreateOrderAction {
    use AsAction;

    protected $data = [];

    public function handle($total, $payment_gateway, $receipt_method, $address_id = null, $notes = null, $exceed_limit = false, $is_heavy_load_time = false) {
        $status = OrderStatus::PENDING;

        return Order::create([
            "status" => $status,
            'branch_id' => Utils::getBranchFromRequestHeader(),
            'customer_id' => auth()->id(),
            'receipt_method' => $receipt_method,
            'payment_status' => 'pending',
            'address_id' => $address_id,
            'total' => $total,
            'date' => now(),
            'notes' => $notes,
        ]);


    }

}
