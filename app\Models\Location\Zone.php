<?php

namespace Tasawk\Models\Location;

use Tasawk\Models\City;
use Tasawk\Models\Customer;
use Tasawk\Traits\Publishable;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Spatie\Translatable\HasTranslations;

class Zone extends Model {
    use HasFactory, HasTranslations, Publishable;

    public array $translatable = ['name'];


    protected $fillable = [
        'name',
        'status'

    ];


    public function customers(){
        return $this->hasMany(Customer::class, 'zone_id');
    }
    public function getCustomersCountAttribute()
    {
        return $this->customers()->count();
    }
    public function cites(){
        return $this->hasMany(City::class,'zone_id');
    }
}
