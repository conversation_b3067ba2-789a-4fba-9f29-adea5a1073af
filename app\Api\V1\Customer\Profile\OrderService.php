<?php

namespace Tasawk\Api\V1\Customer\Profile;


use Api;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Notification;
use Tasawk\Actions\Customer\BuildCartInstanceAction;
use Tasawk\Actions\Customer\PayOrderAction;
use Tasawk\Api\Core;
use Tasawk\Enum\OrderPaymentStatus;
use Tasawk\Enum\OrderStatus;
use Tasawk\Http\Requests\Api\Customer\Order\CancelOrderRequest;
use Tasawk\Http\Requests\Api\Customer\Order\OrderRateRequest;
use Tasawk\Http\Requests\Api\Customer\Order\ReportOrderRequest;
use Tasawk\Http\Requests\Api\Customer\Order\StoreOrderRequest;
use Tasawk\Http\Requests\Api\Customer\Order\UpdateOrderRequest;
use Tasawk\Http\Resources\Api\Customer\Cart\CartResource;
use Tasawk\Http\Resources\Api\Customer\Orders\OrdersResource;
use Tasawk\Lib\Utils;
use Tasawk\Models\FleetWorker;
use Tasawk\Models\Order;
use Tasawk\Models\Service;
use Tasawk\Models\User;
use Tasawk\Models\Worker;

use Tasawk\Notifications\Order\NewOrderCreatedNotification;
use Tasawk\Settings\GeneralSettings;

class OrderService {


    public function index() {
        return Api::isOk(__("Orders list"), OrdersResource::collection(
            auth()->user()->toCustomer->orders()
                ->when(request()->get('status') == 'in_processing', fn($q) => $q->whereIn('status', [OrderStatus::PENDING, OrderStatus::PROBLEMATIC, OrderStatus::IN_PROCESSING, OrderStatus::PENDING_FOR_PAY]))
                ->when(request()->get('status') == 'completed', fn($q) => $q->whereIn('status', [OrderStatus::CANCELED, OrderStatus::COMPLETED]))
                ->latest()->paginate())
        );
    }

    public function show(Order $order) {
        return Api::isOk(__("Order details"))->setData(OrdersResource::make($order));
    }

    public function store(StoreOrderRequest $request): Core {

        // Create the order without services first
        $orderData = $request->validated();

        // Remove services from order data as we'll handle them separately
        unset($orderData['services']);

        // Handle backward compatibility with single service
        if ($request->has('service_id') && !$request->has('services')) {
            $orderData['service_id'] = $request->get('service_id');
        }

        $order = Order::create([
            ...$orderData,
            'date' => $request->get('date', now()),
            'time' => $request->get('time', now()),
            "status" => OrderStatus::PENDING,
            'customer_id' => auth()->id(),
            'payment_status' => 'pending',
            'total' => 0,
            'app_commission_percentage' => (new GeneralSettings())?->app_commission ?? 10,
            'notes' => $request->get('notes', ''),
        ]);

        // Handle multiple services with quantities
        if ($request->has('services')) {
            $this->attachServicesToOrder($order, $request->get('services'));
        }

        $workers = FleetWorker::with("user")
            ->where('account_type', $order->worker_type)
            ->whereHas('categories', fn($q) => $q->where("category_id", $order->category_id))
            ->whereHas('user.wallet', fn($q) => $q->where('balance', '>', 0))
            ->get()
            ->pluck('user');
        Log::info("Workers", $workers->toArray());

        foreach ($request->images ?? [] as $image) {
            $order->addMedia($image)->toMediaCollection();
        }
        try {
            Notification::send($workers, new \Tasawk\Notifications\Worker\NewOrderCreatedNotification($order));

            Notification::send(Utils::getAdministrationUsers(), new NewOrderCreatedNotification($order));
        } catch (\Exception $e) {

        }

        return Api::isOk("Order Details", OrdersResource::make($order));

    }

    public function updateOrder(UpdateOrderRequest $request, Order $order) {
        $order->update([
            ...$request->only("receipt_method", "notes"),
            'payment_data' => array_merge($order?->payment_data ?? [], ['gateway' => $request->get('payment_gateway'), 'method' => '']),
        ]);

        // Handle services update if provided
        if ($request->has('services')) {
            // Remove existing services
            $order->orderServices()->delete();
            // Attach new services
            $this->attachServicesToOrder($order, $request->get('services'));
        }

        if ($request->get('payment_gateway') == 'cash') {
            $order->update(['payment_status' => OrderPaymentStatus::PAID, 'status' => OrderStatus::PENDING]);
        }
        return Api::isOk(__("Order updated"))->setData(OrdersResource::make($order));
    }

    public function rate(OrderRateRequest $request, Order $order) {
        abort_if(!$order->canRate(), 400, __('validation.api.order_cannot_be_rated'));
        $order->rate()->create($request->validated());
        return Api::isOk(__("Thanks for rating order"));

    }

    public function accept(Order $order, Worker $worker) {

        abort_if(!$order->canAccept(), 400, __('validation.api.order_cannot_be_accepted'));

        $order->update(['status' => OrderStatus::IN_PROCESSING, 'worker_id' => $worker->id]);
        try {
            Notification::send($worker, new \Tasawk\Notifications\Customer\OrderAcceptWorkerNotification($order, $worker));
        } catch (\Exception $e) {

        }


        return Api::isOk(__("Order accepted"));

    }

    public function pay(Order $order): Core {
        PayOrderAction::run($order);
        return $this->show($order);
    }

    public function report(ReportOrderRequest $request, Order $order): Core {
        abort_if(!$order->canReport(), 400, __('validation.api.order_cannot_be_reported'));

        $order->report()->create([...$request->validated(), 'user_id' => auth()->id()]);
        $order->update(['status' => OrderStatus::PROBLEMATIC]);
        return Api::isOk('reported');
    }

    public function cancel(CancelOrderRequest $request, Order $order): Core {
        abort_if(!$order->canCancel(), 400, __('validation.api.order_cannot_be_canceled'));
        $order->cancel($request->get('reason_id'), $request->get('note'));
        return Api::isOk('canceled');
    }

    /**
     * Attach multiple services with quantities to an order
     */
    private function attachServicesToOrder(Order $order, array $services): void
    {
        $totalOrderPrice = 0;

        foreach ($services as $serviceData) {
            $service = Service::find($serviceData['service_id']);
            $quantity = $serviceData['quantity'];
            $unitPrice = $service->price->formatByDecimal();
            $totalPrice = $unitPrice * $quantity;

            // Create order service record
            $order->orderServices()->create([
                'service_id' => $service->id,
                'quantity' => $quantity,
                'unit_price' => $unitPrice,
                'total_price' => $totalPrice,
            ]);

            $totalOrderPrice += $totalPrice;
        }

        // Update order total
        $order->update(['total' => $totalOrderPrice]);
    }

}
