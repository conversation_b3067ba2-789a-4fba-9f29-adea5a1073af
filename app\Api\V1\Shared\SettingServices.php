<?php

namespace Tasawk\Api\V1\Shared;

use Carbon\Carbon;
use Illuminate\Support\Collection;
use Str;
use Tasawk\Api\Core;
use Tasawk\Api\Facade\Api;
use Tasawk\Http\Resources\Api\Shared\ContactTypeResource;
use Tasawk\Models\ContactType;
use Tasawk\Settings\GeneralSettings;


class SettingServices {
    public function all(GeneralSettings $settings) {
        return Api::isOk(__("Settings list"), [
            "name" => $settings->app_name,
            'email' => $settings->app_email,
            'address' => $settings->app_address,
            "phone" => $settings->app_phone,
            "whatsapp" => $settings->app_whatsapp,
            'social_media' => $this->socialMedia($settings->social_links),
            'minimum_worker_wallet_balance' => $settings->minimum_worker_wallet_charge,
            'show_subscriptions' => $settings->show_subscriptions,
            'working_days' => collect($settings->working_days[0] ?? [])
                ->filter(fn($el) => $el['status'])
                ->map(function ($el) {
                    return [
                        'day' => Str::headline($el['day_name']),
                        'day_name' => __("forms.fields.weekdays.{$el['day_name']}"),
                        'from' => Carbon::parse($el['from'])->format('H:i'),
                        'to' => Carbon::parse($el['to'])->format('H:i'),
                    ];
                })->values(),
        ]);
    }


    public function socialMedia($links): Collection {
        return collect($links)->map(function ($el) {
            $el['icon'] = asset("images/svg/regular/{$el['icon']}.svg");
            return $el;
        });

    }


    public function contactTypes() {
        return Api::isOk("Types List")->setData(ContactTypeResource::collection(ContactType::enabled()->latest()->paginate()));
    }


}
