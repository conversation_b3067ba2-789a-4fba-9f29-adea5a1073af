<?php

namespace Tasawk\Filament\Resources\Catalog\CategoryResource\RelationManagers;

use Filament\Forms;
use Filament\Forms\Components\Section as SectionComponent;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Form;
use Filament\Notifications\Notification;
use Filament\Resources\Concerns\Translatable;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Actions\Action;
use Filament\Tables\Columns\IconColumn;
use Filament\Tables\Columns\SpatieMediaLibraryImageColumn;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Model;
use Tasawk\Models\Category;
use Tasawk\Traits\Filament\HasTranslationLabel;

class ChildrenRelationManager extends RelationManager {
    use Translatable;
    use HasTranslationLabel;
    protected static string $relationship = 'children';
    public static function getTitle(Model $ownerRecord, string $pageClass): string
    {
        return __('forms.fields.sub_categorys');
    }

    public function form(Form $form): Form {
        return $form
            ->schema([
                SectionComponent::make("basic_information")
                    ->schema([
                        TextInput::make('name')
                            ->required(),
                        Toggle::make('status')
                            ->default(1)
                            ->onColor('success')
                            ->offColor('danger')
                    ])
            ]);
    }

    public function table(Table $table): Table {
        return $table
            ->recordTitleAttribute('id')
            ->columns([
                Tables\Columns\TextColumn::make('id'),
                Tables\Columns\TextColumn::make('name'),
                SpatieMediaLibraryImageColumn::make('image'),
                Tables\Columns\TextColumn::make('created_at'),
                IconColumn::make('status')
                    ->boolean()
                    ->action(
                        Action::make('Active')
                            ->label(fn(Category $record): string => $record->status ? __('panel.messages.deactivate') : __('panel.messages.activate'))
                            // ->disabled(fn(Model $record): bool => !auth()->user()->can('update', $record))
                            ->requiresConfirmation()
                            ->action(fn(Category $record) => $record->toggleStatus())

                    )
            ])
            ->filters([

            ])
            ->headerActions([
            ])
            ->actions([
                Tables\Actions\Action::make('edit')
                    ->label(__('forms.fields.edit'))
                    ->url(fn(Category $record): string => route('filament.admin.resources.catalog.categories.edit', $record->id)),
                Tables\Actions\Action::make('delete')
                    ->label(__('forms.fields.delete'))
                    ->color('danger')
                    ->action(fn(Category $record) => $record->delete()),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->emptyStateActions([

            ]);
    }
    protected function canEdit(Model $record): bool {
        return true;
    }

    protected function canDelete(Model $record): bool {
        return true;
    }

    public static function canViewForRecord(Model $ownerRecord, string $pageClass): bool
    {
        return $ownerRecord?->parent_id == null;
    }
}
