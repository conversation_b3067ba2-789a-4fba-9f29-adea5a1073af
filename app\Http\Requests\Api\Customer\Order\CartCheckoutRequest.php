<?php

namespace Tasawk\Http\Requests\Api\Customer\Order;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;
use Tasawk\Lib\Utils;
use Tasawk\Rules\AddressBelongToAuthUserRule;
use Tasawk\Rules\IsAddressLocationInsideBranchBoundaries;
use Tasawk\Rules\IsDayDateInWorkingDateRule;
use Tasawk\Rules\IsProductAvailableInBranchRule;
use Tasawk\Rules\IsRequiredProductOptionsRepresentRule;
use Tasawk\Rules\IsValidProductOptionsRule;
use Tasawk\Rules\IsValidProductOptionValuesRule;
use Tasawk\Settings\GeneralSettings;


class CartCheckoutRequest extends FormRequest {

    public function authorize() {
        return true;
    }

    protected function prepareForValidation() {
        $settings = new GeneralSettings;

        $this->merge([
            'date' => now(),
            'branch_id' => Utils::getBranchFromRequestHeader(),
            'exceed_limit' => $this->collect('products')->max('quantity') > $settings->order_limit_per_item,
            'is_heavy_load_time' => Utils::branchInHeavyLoadMode(),
        ]);
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules(): array {
        return [
            "receipt_method" => ['required', Rule::in(Utils::getBranchReceiptMethods())],
            'payment_gateway' => ['required', 'in:myfatoorah,cash'],
            "address_id" => [Rule::requiredIf($this->get('receipt_method') == 'delivery'), new AddressBelongToAuthUserRule],
            'products' => ['required', 'array'],
            'products.*.id' => ['required', new IsProductAvailableInBranchRule(), new IsRequiredProductOptionsRepresentRule()],
            'products.*.quantity' => ['required', 'numeric', 'min:1'],
            'products.*.options.*.id' => [new IsValidProductOptionsRule()],
            'products.*.options.*.value_id' => [new IsValidProductOptionValuesRule()],
            'date' => ['required', new IsDayDateInWorkingDateRule()],
            'branch_id' => ['required', new IsAddressLocationInsideBranchBoundaries()],
            'notes' => ['nullable'],
            'exceed_limit' => ['nullable', 'boolean']
        ];
    }

}
