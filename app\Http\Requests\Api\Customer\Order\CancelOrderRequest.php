<?php

namespace Tasawk\Http\Requests\Api\Customer\Order;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;
use Tasawk\Lib\Utils;
use Tasawk\Rules\AddressBelongToAuthUserRule;
use Tasawk\Rules\IsAddressLocationInsideBranchBoundaries;
use Tasawk\Rules\IsDayDateInWorkingDateRule;
use Tasawk\Rules\IsProductAvailableInBranchRule;
use Tasawk\Rules\IsRequiredProductOptionsRepresentRule;
use Tasawk\Rules\IsValidProductOptionsRule;
use Tasawk\Rules\IsValidProductOptionValuesRule;
use Tasawk\Settings\GeneralSettings;


class CancelOrderRequest extends FormRequest {

    public function authorize() {
        return true;
    }


    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules(): array {
        return [
            "reason_id" => ['nullable','exists:cancellation_reasons,id'],
            'note' => [Rule::requiredIf(!$this->filled('reason_id') ), 'string', 'max:512']


        ];
    }

}
