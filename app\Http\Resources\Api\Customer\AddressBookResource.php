<?php

namespace Tasawk\Http\Resources\Api\Customer;

use Illuminate\Http\Resources\Json\JsonResource;
use Tasawk\Http\Resources\Api\Shared\ZoneResource;

class AddressBookResource extends JsonResource {
    /**
     * Transform the resource into an array.
     *
     * @param \Illuminate\Http\Request $request
     * @return array
     */
    public function toArray($request) {
        return [
            "id" => $this->id,
            "name" => $this->name,
            "phone" => $this->phone,
            "city"=>CityResource::make($this->district->city),
            "district"=>CityResource::make($this->district),
            "location" => $this->map_location,
            'notes'=>$this->notes,
        ];
    }
}
