<?php

namespace Tasawk\Filament\Resources\Reports\WorkerWalletRequestResource\Pages;


use Filament\Actions\Action;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Tabs;
use Filament\Forms\Components\TextInput;
use Filament\Resources\Pages\ListRecords;
use Tasawk\Filament\Resources\Reports\WorkerWalletRequestResource;
use Tasawk\Filament\Resources\Reports\WorkerWalletResource;

class ListWorkerWalletRequests extends ListRecords {
    protected static string $resource = WorkerWalletRequestResource::class;

    protected function getHeaderActions(): array {
        return [

        ];
    }
}
