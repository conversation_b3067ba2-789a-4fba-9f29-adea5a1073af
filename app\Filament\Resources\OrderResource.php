<?php

namespace Tasawk\Filament\Resources;

use BezhanSalleh\FilamentShield\Contracts\HasShieldPermissions;
use Filament\Actions\DeleteAction;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Infolists\Components\Grid;
use Filament\Infolists\Components\Section;
use Filament\Infolists\Components\TextEntry;
use Filament\Infolists\Infolist;
use Filament\Notifications\Notification;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Actions\Action;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Filters\Filter;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use pxlrbt\FilamentExcel\Actions\Tables\ExportBulkAction;
use pxlrbt\FilamentExcel\Exports\ExcelExport;
use Str;
use Tasawk\Enum\OrderPaymentStatus;
use Tasawk\Enum\OrderStatus;
use Tasawk\Enum\ReceiptMethods;
use Tasawk\Filament\Resources\OrderResource\Pages;
use Tasawk\Filament\Resources\OrderResource\RelationManagers\ItemsLineRelationManager;
use Tasawk\Models\CancellationReason;
use Tasawk\Models\Category;
use Tasawk\Models\Manager;
use Tasawk\Models\Order;
use Tasawk\Models\Service;
use Tasawk\Models\Worker;
use Tasawk\Notifications\Order\OrderCanceledNotification;
use Tasawk\Traits\Filament\HasTranslationLabel;

class OrderResource extends Resource implements HasShieldPermissions {
    use HasTranslationLabel;

    protected static ?string $model = Order::class;

    protected static ?string $navigationIcon = 'heroicon-o-building-storefront';
    protected static ?int $navigationSort = 1;

    public static function form(Form $form): Form {
        return $form
            ->schema([
                //
            ]);
    }

    public static function table(Table $table): Table {

        return $table
            ->columns([
                TextColumn::make('order_number')->searchable(['id']),
                TextColumn::make('customer.name')->searchable(),
                TextColumn::make('worker.name')->searchable(),

                TextColumn::make('service.title')
                    ->label(__('forms.fields.service'))
                    ->formatStateUsing(fn($record) => $record->category->name . "- " . $record->service->title)
                    ->searchable(),
                TextColumn::make('date')
                    ->date()
                    ->searchable(),
                TextColumn::make('status')
                    ->badge()
                    ->color(fn($state) => $state->getColor())
                    ->formatStateUsing(function ($record) {
                        return $record->getRenderStatus();
                    })
                    ->searchable(),
                TextColumn::make('payment_status')
                    ->badge()
                    ->color(fn($state) => $state->getColor())
                    ->searchable(),
                TextColumn::make('total')
                    ->money()
                    ->searchable(),
                TextColumn::make('total')
                    ->formatStateUsing(fn($record) => $record?->total?->format())
                    ->summarize(Tables\Columns\Summarizers\Sum::make()->money('SAR')),

            ])
            ->filters([
                SelectFilter::make('worker_id')
                    ->multiple()
                    ->options(Worker::pluck("name", "id")),
//                SelectFilter::make('category_id')
//                    ->label(__("forms.fields.category"))
//                    ->options(Category::pluck("name", "id")),

                SelectFilter::make('service_id')
                    ->label(__("forms.fields.services"))
                    ->multiple()
                    ->options(Service::pluck("title", "id")),
                SelectFilter::make('payment_status')
                    ->multiple()
                    ->options(OrderPaymentStatus::class),

                Filter::make('created_at')
                    ->form([
                        TextInput::make('code')
                            ->label(__("forms.fields.code")),
                        DatePicker::make('date_from'),
                        DatePicker::make('date_until'),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['code'] ?? '',
                                fn(Builder $query, $code): Builder => $query->whereHas('worker.fleetAccount', fn(Builder $query) => $query->where('code', $code)))
                            ->when(
                                $data['date_from'] ?? '',
                                fn(Builder $query, $date): Builder => $query->whereDate('date', '>=', $date),
                            )
                            ->when($data['date_until'] ?? '', fn(Builder $query, $date): Builder => $query->whereDate('date', '<=', $date));
                    })
            ])
            ->actions([

                Action::make('print')
                    ->label(__('panel.actions.print'))
                    ->icon('heroicon-o-printer')
                    ->color('secondary')
                    ->disabled(fn(Order $record) => $record->status != OrderStatus::COMPLETED)
                    ->url(fn(Order $record) => route('orders.invoice', [$record]), true),

                Tables\Actions\ViewAction::make(),
                Tables\Actions\DeleteAction::make()
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    ExportBulkAction::make()->exports([
                        ExcelExport::make("CSV")
                            ->fromTable()
                            ->withFilename(fn() => static::getPluralLabel() . '-' . now()->format('Y-m-d'))
                            ->withWriterType(\Maatwebsite\Excel\Excel::XLSX),


                    ]),
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    static public function infolist(Infolist $infolist): Infolist {
        return $infolist
            ->schema([
                Grid::make()->schema([
                    Section::make("basic_information")
                        ->schema([
                            TextEntry::make('order_number'),
                            TextEntry::make('customer.name')->url(fn(Order $record): string => route('filament.admin.resources.crm.customers.index', ['tableSearch' => $record->customer->id])),
                            TextEntry::make('worker.name'),

                            TextEntry::make('address.name')->visible(fn($record) => $record->address_id)
                                ->formatStateUsing(fn($record) => $record->address->name)
                                ->url(fn(Order $record): string => route('filament.admin.resources.crm.address-books.index', [$record->address_id])),
                            TextEntry::make('date')->date(),
                            TextEntry::make('time')->time(),
                            TextEntry::make('order_type')->formatStateUsing(fn($record) => $record->order_type->getLabel()),
                            TextEntry::make('worker_type')->formatStateUsing(fn($record) => $record->worker_type->getLabel()),

                            TextEntry::make('status')
                                ->badge()
                                ->formatStateUsing(fn($record) => $record->status == OrderStatus::CANCELED ? $record->status->getLabel() . ' - ' . $record->cancellation->reason?->name : $record->status->getLabel())
                                ->color(fn($state) => $state->getColor()),
                            TextEntry::make('report')
                                ->visible(fn($record) => $record->report()->exists())
                                ->label(__("menu.reportreason"))
                                ->formatStateUsing(fn($record) => $record->report->reason->name ?? $record->report->note),
                            TextEntry::make('payment_status')
                                ->badge()
                                ->color(fn($state) => $state->getColor()),
                            TextEntry::make('subTotal')
                                ->state(fn(Order $record) => $record->as_cart->formattedTotals()['subtotal']),
                            TextEntry::make('taxes')
                                ->state(fn(Order $record) => $record->as_cart->formattedTotals()['taxes']),
                            TextEntry::make('total')
                                ->state(fn(Order $record) => $record->as_cart->formattedTotals()['total']),

                        ])->columns(3),
                    Section::make("rate")
                        ->schema([
                            TextEntry::make('name')
                                ->state(fn($record) => $record->rate->rate)
                                ->label(fn($record) => __("forms.fields.rate")),

                            TextEntry::make('comment')
                                ->state(fn($record) => $record->rate->comment)

                        ])
                        ->visible(fn($record) => $record->rated())
                        ->columns(3),
                ])->columns(5)
            ]);
    }

    public static function getRelations(): array {
        return [
            ItemsLineRelationManager::class,

        ];
    }

    public static function getNavigationBadge(): ?string {
        return static::getModel()::where('status', OrderStatus::PENDING)->count();
    }


    public static function getPages(): array {
        return [
            'index' => Pages\ListOrders::route('/'),
//            'create' => Pages\CreateOrder::route('/create'),
//            'edit' => Pages\EditOrder::route('/{record}/edit'),
            'view' => Pages\ViewOrder::route('/{record}/view')
        ];
    }

    public static function getPermissionPrefixes(): array {
        return [
            'view_any',
            'view',
            'update',
            'delete',
            'delete_any',
        ];
    }

    public static function getGloballySearchableAttributes(): array {
        return ['id', 'customer.name'];
    }

    public static function getGlobalSearchResultTitle(Model $record): string {
        return "#" . " " . $record->id . ' - ' . $record->customer->name;
    }
//    public static function getGlobalSearchResultDetails(Model $record): array {
//        return [
//            __('forms.fields.order_number') => $record->order_number,
//            __('forms.fields.customer_name') => $record->customer->name,
//        ];
//    }
    public static function canView(Model $record): bool {
        return true;
    }
}
