<?php

namespace Tasawk\Api\V1\Customer;

use Api;
use Tasawk\Actions\Customer\RegisterCustomer;
use Tasawk\Actions\Shared\Authentication\ForgetPassword;
use Tasawk\Actions\Shared\Authentication\SendVerificationCode;
use Tasawk\Actions\Shared\Authentication\UpdateUserPassword;
use Tasawk\Actions\Shared\Authentication\UpdateUserToken;
use Tasawk\Actions\Shared\Authentication\VerifyUserAccount;
use Tasawk\Api\Core;
use Tasawk\Http\Requests\Api\Customer\Auth\CodeConfirmRequest;
use Tasawk\Http\Requests\Api\Customer\Auth\ForgetPasswordRequest;
use Tasawk\Http\Requests\Api\Customer\Auth\LoginRequest;
use Tasawk\Http\Requests\Api\Customer\Auth\RegisterCustomerRequest;
use Tasawk\Http\Requests\Api\Customer\Auth\ResetPasswordRequest;
use Tasawk\Http\Requests\Api\Customer\Auth\SendOTPRequest;
use Tasawk\Http\Requests\Api\Customer\Auth\VerifyAccountRequest;
use Tasawk\Http\Resources\Api\Customer\CustomerResource;
use Tasawk\Lib\SMS;
use Tasawk\Lib\Utils;
use Tasawk\Models\Customer;
use Tasawk\Models\VerificationCode;

class AuthServices {

    public function login(LoginRequest $request) {
        $is_registered = $customer = Customer::where('phone', $request->get('phone'))->first();


        SendVerificationCode::run($customer, $request->get('phone'));

        return Api::isOk(__("OTP sent"))->setData([
            'is_registered' => (boolean)$is_registered,
        ]);
    }


    public function verifySMSCode(CodeConfirmRequest $request): Core {
        return Api::isOk(__("Correct Verification code"));

    }

    public function register(RegisterCustomerRequest $request) {

        $customer = RegisterCustomer::run(...$request->only("full_name", 'email', 'password', 'phone', 'city_id', 'device_token'));
        UpdateUserToken::run($customer);
        return Api::isOk(__("Customer data"), CustomerResource::make($customer));
    }

    public function verify(VerifyAccountRequest $request) {
        VerifyUserAccount::run($request->currentUser());
        return Api::isOk(__("Verified,User information"))->setData(new CustomerResource($request->currentUser()));

    }

    public function forgetPassword(ForgetPasswordRequest $request): Core {
        ForgetPassword::run($request->currentUser());
        return Api::isOk(__("SMS code sent"));

    }

    public function resetPassword(ResetPasswordRequest $request): Core {
        UpdateUserPassword::run($request->currentUser(), $request->get('password'));
        return Api::isOk(__("User information"))->setData(new CustomerResource($request->currentUser()));

    }

    public function sendOTP(SendOTPRequest $request): Core {

        $code = Utils::randomOtpCode();

        VerificationCode::where(['phone' => $request->get('phone')])->delete();
        VerificationCode::create(['phone' => $request->get('phone'), "code" => $code]);
        SMS::init($request->get('phone'), "Hoomi - Your OTP Code $code")->send();
        return Api::isOk(__("OTP sent"))->setData([]);

    }

}
