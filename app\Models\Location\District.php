<?php

namespace Tasawk\Models\Location;

use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Matan<PERSON><PERSON>ev\EloquentSpatial\Objects\Geometry;
use Matan<PERSON>adaev\EloquentSpatial\Objects\Point;
use Matan<PERSON>adaev\EloquentSpatial\Objects\Polygon;
use Matan<PERSON><PERSON>ev\EloquentSpatial\Traits\HasSpatial;
use Spatie\Translatable\HasTranslations;
use Tasawk\Traits\Publishable;
use Tasawk\Models\Occasions\Occasion;
use Tasawk\Models\Customer;
use Spatie\MediaLibrary\InteractsWithMedia;

class District extends Model {
    use Publishable, HasTranslations;
    use HasSpatial;

    public array $translatable = ['name'];
    protected $fillable = [
        'name',
        'city_id',
        'status',
    ];

    public function city(): BelongsTo {
        return $this->belongsTo(City::class);
    }
    public function customers(){
        return $this->hasMany(Customer::class, 'district_id');
    }
    public function getCustomersCountAttribute()
    {
        return $this->customers()->count();
    }

}
