<?php

namespace Tasawk\Models\Order;


use Tasawk\Models\User;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Report extends Model {

    protected $guarded = ['id'];

    protected $table = 'orders_report';

    public function reason(): BelongsTo {
        return $this->belongsTo(ReportReason::class, 'reason_id');
    }
    public function user () {
        return $this->belongsTo(User::class, 'user_id');
    }


}
