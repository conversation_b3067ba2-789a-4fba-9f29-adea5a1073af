<?php

namespace Tasawk\Models;

use Cknow\Money\Money;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;
use Spatie\Translatable\HasTranslations;
use Tasawk\Traits\Publishable;

class Service extends Model implements HasMedia {
    use HasTranslations, Publishable, InteractsWithMedia, SoftDeletes;

    protected $fillable = ['title', 'status', 'category_id', 'price'];
    public array $translatable = ['title'];

    public function category(): BelongsTo {
        return $this->belongsTo(Category::class);
    }

    public function price(): Attribute {
     return    Attribute::make(get: fn($value) =>Money::parse($value));
    }
}
