<?php

namespace Tasawk\Filament\Pages\Settings;

use <PERSON><PERSON>han<PERSON><PERSON>h\FilamentShield\Traits\HasPageShield;
use Tasawk\Settings\GeneralSettings;
use Tasawk\Settings\ThirdPartySettings;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Pages\SettingsPage;
use Illuminate\Contracts\Support\Htmlable;

class ManageThirdParty extends SettingsPage {
    use HasPageShield;
    protected static ?string $navigationIcon = 'heroicon-o-link';
    protected static string $settings = ThirdPartySettings::class;
    protected static ?string $slug = 'settings/third-party';
    protected static ?int $navigationSort = 3;
    public function form(Form $form): Form {
        return $form
            ->schema([
                Section::make("General")->schema([

                    TextInput::make('firebase_server_key')
                        ->columnSpan(['xl' => 2]),

                    TextInput::make('firebase_server_id')
                        ->columnSpan(['xl' => 2]),

                    TextInput::make('google_map_key')
                        ->columnSpan(['xl' => 2]),
                ]),
                Section::make("sms")

                    ->description("taqnyat")
                    ->schema([

                        TextInput::make('sms.sender_name')->label(__('forms.fields.sender_name')),

                        TextInput::make('sms.bearer_token')->label(__('forms.fields.bearer_token')),
                    ]),
            ]);
    }
    public static function getNavigationLabel(): string {
        return __("menu.third_party");
    }
    public static function getNavigationGroup(): ?string {
        return __('menu.settings');
    }
    public function getHeading(): string|Htmlable {
        return __('sections.manage_third_party');
    }
    public function getTitle(): string|Htmlable {
        return __('sections.manage_third_party');
    }
    public function getBreadcrumbs(): array {
        return [
            null =>static::getNavigationGroup(),
            static::getUrl() => static::getNavigationLabel(),
        ];
    }
}
