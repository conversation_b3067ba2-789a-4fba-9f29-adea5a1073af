<?php

namespace Tasawk\Filament\Resources\Locations\DistrictResource\Pages;

use Filament\Actions;
use Filament\Resources\Pages\CreateRecord;
use Filament\Resources\Pages\CreateRecord\Concerns\Translatable;
use Tasawk\Filament\Resources\Locations\DistrictResource;

class CreateDistrict extends CreateRecord
{
    use Translatable;
    protected static string $resource = DistrictResource::class;

    protected function getHeaderActions(): array {
        return [
            Actions\LocaleSwitcher::make(),
        ];
    }
    protected function getRedirectUrl(): string {
        return $this->getResource()::getUrl("index");
    }
}
