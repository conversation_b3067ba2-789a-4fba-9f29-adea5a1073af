<?php
return [
    'messages' => [
        "admin_accept_balance_request_text" => "You have accepted the withdrawal request of :AMOUNT SAR from the wallet",
        'add_wallet_balance'=>'You have added :amount SAR to your wallet',
        'add_order_dues'=>'You have added :amount SAR to your wallet for order no :id',
        'not_support_receipt_method'=>'Receipt method not supported',
        'deactivated' => 'Deactivated',
        'deactivate' => 'Deactivate',
        'activate' => 'Activate',
        'activated' => 'Activated',
        'send_notifications_description' => 'You can send real time notification to your customers',
        'success' => 'Success',
        'notifications_sent_successfully' => 'Notifications sent successfully',
        'expected_time_of_arrival' => "From :from to :to minutes",
        'customer_not_responded' => 'Customer not responded',
        'manager_not_responded' => 'Manager not responded',
        'warning' => 'Warning',
        'branch_has_orders' => 'You cannot delete branch :branch for it has orders',
        'product_belong_to_many_orders' => 'You cannot delete product :product for it has orders',
        'option_belong_to_many_branches' => 'You cannot delete option :option for it has branches',
        'zone_belong_to_many_branches' => 'You cannot delete zone :zone for it has branches',
        'customer_has_many_order' => 'You cannot delete customer :customer for it has orders',
        'no_data_found_product_options' => 'there is no options linked to current product,please go to product details and attach some options',
        "product_belong_to_many_order" => "You cannot delete product :product for it has orders",
        'estimated_time'=>':FROM - :TO minutes',
        'category_has_many_products'=>'You cannot delete category :category for it has products',
        'allergen_has_many_products'=>'You cannot delete allergen :allergen for it has products',
    ],
    'languages' => [
        'arabic' => 'Arabic',
        'english' => 'English',
    ],
    'actions' => [
        'change_status' => 'Change status',
        'cancel' => 'Cancel',
        'print'=>'Print',
        'add_operation' => 'Add operation',
        'accept' => 'Accept',
        'reject' => 'Reject',

    ],
    'enums' => [
        'NULL'=>'',
        'rejected' => 'Rejected',
        'approved' => 'Approved',
        'normal'=>"Normal",
        'INDIVIDUAL'=>'Individual',
        'COMPANY'=>'Company',
        'PROFESSIONAL'=>'Professional',
        'OTHER'=>'Other',
        'SAUDI'=>'Saudi',
        'immediately'=>'Immediately',
        'deposit'=>'Deposit',
        'withdraw'=>'Withdraw',
        'pending' => 'Pending',
        "in_processing" => "In processing",
        "pending_for_pay" => "Pending for pay",
        'problematic' => 'Problematic',
        'completed' => 'Completed',
        'paid' => 'Paid',
        'canceled' => 'Canceled',
        'customer'=>'Customer',
        'worker'=>'Worker',
        'all' => 'All',
        'ACTIVE' => 'Active',
        'INACTIVE' => 'Inactive',
        'radio' => 'Radio',
        'checkbox' => 'Checkbox',
        'credit_card'=>'Credit card',
        'cash'=>'Cash',
        'received'=>'Received',
    ],
    'notifications' => [
        "worker_new_order_arrival"=>"New order",
        "worker_new_order_arrival_text"=>"New order arrived",
        "worker_accept_order"=>"Order accepted",
        "worker_accept_order_text"=>"Order  accepted from :NAME",
        "customer_accept_worker_order"=>"تم اختيارك لتنفيذ الطلب",
        "customer_accept_worker_order_text"=>"تم اختيارك لتنفيذ الطلب رقم :id",
        'new_order_arrival' => 'New order',
        "new_order_arrival_pending_for_arrival" => 'New order arrived pending for approved',
        "order_accepted" => "Order no :id accepted",
        "order_accepted_body" => "Thank you :customer_name. Please complete the payment process to complete your order",
        "order_canceled" => "Order no :id canceled",
        "order_canceled_body" => "Sorry, your order cannot be received at the moment by the branch manager due to :reason",
        "branch_maintenance_mode_changed" => "Branch :branch_name maintenance mode changed",
        "branch_maintenance_mode_changed_body" => "Branch :branch_name maintenance mode changed to :status",
        "branch_manager_change_product_status" => "Product status changed in branch :branch_name",
        "branch_manager_change_product_status_body" => "Product :product_name status changed in branch :branch_name to :status",
        "branch_manager_change_product_option_status" => "Product :product_name option status changed in branch :branch_name",
        "branch_manager_change_product_option_status_body" => "Product :product_name option :option_name status changed in branch :branch_name to :status",
        "new_order_not_accepted_yet" => "There is an order not accepted yet",
        "new_order_not_accepted_yet_body" => "Order no :id not accepted yet",
        "order_not_responded" => "Sorry, your order no :id cannot be completed",
        "order_not_responded_body" => "Due to the unavailability of some products in your order, you can order from another branch",
        "new_customer_registered" => "Congratulations",
        "new_customer_registered_body" => "Your account has been successfully registered in the Kufa application, order now and enjoy the most delicious food",
        "new_product_created_title" => "New product created",
        "new_product_created_body" => " :product_name added, you can try it now",
        "order_status_changed" => "Order no :id status changed",
        "order_status_changed_body" => "Order  status changed to :status",
        "otp_code_sent_title" => 'OTP code sent',
        "otp_code_sent_body" => "OTP code sent,check your phone",
    ],
    'stats' => [
        'workers_requests_count' => 'Workers requests count',
        'customer_count' => 'Customer count',
        'problematic_orders_total' => 'Problematic orders total',
        'new_orders_total' => 'New orders total',
        'in_processing_orders_total' => 'In processing orders total',
        'completed_orders_total' => 'Completed orders total',
        'canceled_orders_total' => 'Canceled orders total',
        'workers_count' => 'Workers count',
        'total_orders' => 'Total orders',
        'new_orders_count' => 'New orders count',
        'in_processing_orders_count' => 'In processing orders count',
        'pending_orders_count' => 'Pending orders count',
        'completed_orders_count' => 'Completed orders count',
        'canceled_orders_count' => 'Canceled orders count',
        'problematic_orders_count' => 'Problematic orders count',
        'orders' => 'Orders',
        'today' => 'Today',
        'week' => 'This week',
        'last_month' => 'Last month',
        'year' => 'This year',
        'customers' => 'Customers',
        'orders_chart_description' => 'Number of orders during the specified period',
        'customers_description' => 'Number of customers during the specified period',
        'order_receipt_methods' => 'Order receipt methods',
        'order_receipt_methods_description' => 'A chart showing the number of orders received by each method',
        'orders_group_by_branch' => 'Orders group by branch',
        'orders_group_by_branch_description' => 'A chart showing the number of orders received by each branch',
    ],
    'widgets' => [
        'global_order_stats'=>'Global order stats',
        'order_stats' => 'Order stats',
        'order_receipt_methods' => 'Order receipt methods',
        'orders_chart' => 'Orders chart',
        'customers_chart' => 'Customers chart',
        'latest_orders' => 'Latest orders',
        'best_selling_products' => 'Best selling products',
        'top_branches' => 'Top branches',
        'branches_in_heavy_load_modes' => 'Branches in heavy load modes',
        'branches_in_maintenance_modes' => 'Branches in maintenance modes',
        'contacts' => 'Contacts',
        'orders_group_by_branches' => 'Orders group by branches',
    ],
];
