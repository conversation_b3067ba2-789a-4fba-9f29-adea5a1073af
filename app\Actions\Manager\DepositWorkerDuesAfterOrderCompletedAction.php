<?php

namespace Tasawk\Actions\Manager;

use Lorisleiva\Actions\Concerns\AsAction;
use MyFatoorah\Library\MyfatoorahApiV2;
use MyFatoorah\Library\PaymentMyfatoorahApiV2;
use Tasawk\Models\Order;
use Tasawk\Models\User;

class DepositWorkerDuesAfterOrderCompletedAction {
    use AsAction;

    public function handle(Order $order) {
        $dues = $order->worker_dues;
        $order->worker->deposit($dues, [
            'description' => [
                'ar' => __("panel.messages.add_order_dues", ["amount" => $dues, 'id' => $order->id], 'ar'),
                'en' => __("panel.messages.add_order_dues", ["amount" => $dues, 'id' => $order->id], 'en')
            ],
            'order_id' => $order->id
        ]);


    }

}
