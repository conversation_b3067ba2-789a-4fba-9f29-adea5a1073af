<?php

namespace Tasawk\Enum;

use Filament\Support\Contracts\HasLabel;

enum OrderStatus: string implements HasLabel {
    case PENDING = 'pending';
    case IN_PROCESSING = 'in_processing';
    case PENDING_FOR_PAY = 'pending_for_pay';
    case PROBLEMATIC = 'problematic';
    case CANCELED = 'canceled';
    case COMPLETED = 'completed';

    public function getLabel(): ?string {
        return __("panel.enums.$this->value");
    }

    public function getColor(): string {
        return match ($this->value) {
            'PENDING', => 'warning',
            'PENDING_FOR_PAY', 'IN_PROCESSING' => 'primary',
            'completed' => 'success',
            default=>'danger'
        };

    }

}
