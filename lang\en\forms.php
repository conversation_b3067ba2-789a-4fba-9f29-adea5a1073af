<?php
return [
    'fields' => [
        "user_balance" => "User balance",
        "request_amount" => "Request amount",
        "worker_phone" => "Worker phone",
        "current_worker_balance_is" => "Current worker balance is :BALANCE SAR",
        "code" => "Code",
        "logo_light" => "Logo light",
        "sender_name" => "Sender name",
        "bearer_token" => "Bearer token",
        "logo_dark" => "Logo dark",
        "bill_image" => "Bill image",
        'district_name' => 'District',
        'worker_type' => "Worker type",
        'account_type' => "Account type",
        'nationality' => "nationality",
        'residence_type' => 'residence type',
        'bank_account' => "bank account",
        'commercial_registration_number' => "commercial registration number",
        'commercial_registration_image' => 'commercial registration image',
        'approve_letter_image' => 'approve letter image',
        'time' => 'Time',
        'total_without_vat' => 'Total without vat',
        'is_active' => 'Is active',
        'category' => 'Category',
        "services" => "Services",
        'worker_name' => 'Worker name',
        'worker_id' => 'Worker',
        'show_invoice' => 'Show invoice',
        'invoice_url' => 'Invoice url',
        'amount_of_minutes_to_cancel_order' => 'Amount of minutes to cancel order',
        'other' => 'Other',
        'cancellation_reason_id' => 'Cancellation reason',
        'order_type' => 'Order type',
        'operation_type' => 'Operation type',
        'worker_dues' => 'Worker dues',
        'totals' => 'Totals',
        'holder_name' => 'Holder name',
        'balance' => 'Balance',
        'amount' => 'Amount',
        'for' => 'For',
        'address' => 'Address',
        'city_id' => 'City',
        'created_at' => 'Created at',
        'edit' => 'Edit',
        'delete' => 'Delete',
        'sub_categories' => 'Sub categories',
        'app_commission' => 'App commission',
        'minimum_worker_wallet_charge' => 'Minimum worker wallet charge',
        'available' => 'Available',
        'city_name' => 'City',
        "review" => "Review",
        "rate" => "Rate",
        'sort' => 'Sort',
        'id' => 'ID',
        'title' => 'Title',
        'text' => 'Text',
        'address_name' => 'Address name',
        'image' => 'Image',
        'avatar' => 'Avatar',
        'name' => 'Name',
        'user_id' => 'User',
        'logo' => 'App logo',
        'order_total' => 'Order total',
        'print_invoice' => 'Print invoice',
        'net_profit' => 'Net profit',

        'question' => 'Question',
        'answer' => 'Answer',
        'app_address' => 'App address',
        'type' => 'Type',
        'category_parent_id' => 'Parent Category',
        'email' => 'Email',
        'takeaway_discount' => 'Takeaway discount',
        'phone' => 'Phone',
        'active' => 'Active',
        'customer_name' => 'Customer',
        'zone_name' => 'Zone',
        'location' => 'Location',
        'map_location' => 'Map location',
        'state' => 'State',
        'managers' => 'Managers',
        'receipt_method' => 'Receipt method',
        'maintenance_mode' => 'Maintenance mode',
        'heavy_load_mode' => 'Heavy load mode',
        'street' => 'Street',
        'building_number' => 'Building number',
        'floor' => 'Floor',
        'primary' => 'Primary',
        'password' => 'Password',
        'password_confirmation' => 'Password confirmation',
        'notes' => 'Notes',
        'product_title' => 'Product_title',
        'product_name' => 'Product name',
        'from' => 'From',
        'to' => 'To',
        'status' => 'Status',
        'options' => 'Options',
        'required' => 'Required',
        'values' => 'Values',
        'value_name' => 'Value name',
        'price' => 'Price',
        'option_name' => 'Option name',
        'weekdays' => [
            'saturday' => 'Saturday',
            'sunday' => 'Sunday',
            'monday' => 'Monday',
            'tuesday' => 'Tuesday',
            'wednesday' => 'Wednesday',
            'thursday' => 'Thursday',
            'friday' => 'Friday',
        ],
        'sale_price' => 'Sale price',
        'categories' => 'Categories',
        'allergens' => 'Allergens',
        'description' => 'Description',
        'branch' => 'Branch',
        'type_name' => 'Type name',
        'message' => 'Message',
        'seen' => 'Seen',
        'app_logo' => 'App logo',
        'app_name' => 'App name',
        'app_email' => 'App email',
        'app_phone' => 'App phone',
        'enable_delivery_mode' => 'Enable delivery mode',
        'enable_orders_discount_upon_receipt_from_the_branch' => 'Enable orders discount upon receipt from the branch',
        'delivery_cost' => 'Delivery cost',
        'orders_discount_percentage_upon_receipt_from_the_branch' => 'Orders discount percentage upon receipt from the branch',
        'diameter' => 'Diameter',
        'delivery_cost_for_each_additional_kilometer' => 'Delivery cost for each additional kilometer',
        'order_limit_per_item' => 'Orders limit per item',
        'google_play_link' => 'Google play link',
        'apple_store_link' => 'Apple store link',
        'about_us' => 'About us',
        'terms_and_conditions' => 'Terms and conditions',
        'privacy_policy' => 'Privacy policy',
        'icon' => 'Icon',
        'link' => 'Link',
        'notifiable' => 'Notifiable',
        'notification_title' => 'Notification title',
        'notification_body' => 'Notification body',
        'recommendations' => 'Recommendations',
        'payment_status' => 'Payment status',
        'total' => 'Total',
        'quantity' => 'Quantity',
        'subTotal' => 'SubTotal',
        'taxes' => 'Taxes',
        'delivery' => 'Delivery',
        'reason_id' => 'Reason',
        "comment" => 'Comment',
        'branch_name' => 'Branch name',
        'date' => 'Date',

        'food_quality' => 'Food quality',
        'service_quality' => 'Service quality',
        "delivery_speed" => 'Delivery speed',
        "serving_food_quality" => 'Serving food quality',
        'today_orders' => 'Today orders',
        'min_time_to_deliver_order' => 'Min time to deliver order',
        'max_time_to_deliver_order' => 'Max time to deliver order',
        'role' => 'Role',
        'roles_name' => 'Role',
        'receipt_methods' => 'Receipt methods',
        'message_title' => 'Message title',
        'message_body' => 'Message body',
        'firebase_server_key' => 'Firebase server key',
        'firebase_server_id' => 'Firebase server id',
        'google_map_key' => 'Google map key',
        'enable_maintenance_mode' => 'Enable maintenance mode',
        'enable_heavy_load_mode' => 'Enable heavy load mode',
        'mark_as_seen' => 'Mark as seen',
        'orders_count' => 'Orders count',
        'index' => 'Index',
        'sub_categorys' => 'Sub categories',
        'order_number' => 'Order number',
        'message_on_order_created' => 'Message on order created',
        'message_on_order_on_the_way' => 'Message on order on the way',
        'message_on_order_is_delivered' => 'Message on order is delivered',
        'message_on_receipt_from_branch' => 'Message on receipt from branch',
    ],
    'tooltip' => [
        'order_limit_per_item' => 'After exceeding the limit, the customer will not pay until approved by one of the branch managers',
        'diameter' => 'The maximum delivery distance is between the customer’s location and the branch from which the order is requested',
    ],
    'suffixes' => [
        'sar' => 'SAR',
        'kcal' => 'Kcal',
        'km' => 'KM',
        'minutes' => 'Minutes'
    ],
    'actions' => [
        "accept" => "Accept",
        "reject" => "Reject",
        'add' => 'Add',
        'send_notifications' => 'Send notifications',
        'send' => 'Send',
    ],
    'options' => [
        'all' => 'All',
        'specific' => 'Specific',
    ],


];
