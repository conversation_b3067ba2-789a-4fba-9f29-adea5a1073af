<header>
    <div class="container">
        <div class="header-content">
            <?php echo $__env->yieldContent('logo'); ?>
            <div class="navigation">
                <ul class=" big-menu list-unstyled ">
                    <li><a href="<?php echo e(route('home')); ?>#about-sec"><?php echo e(trans('site.about_us')); ?> </a></li>
                    <li><a href="<?php echo e(route('home')); ?>#features-sec"> <?php echo e(trans('site.app_feature')); ?> </a></li>
                    <li> <a href="<?php echo e(route('home')); ?>#appScreens-sec"> <?php echo e(trans('site.app_images')); ?></a> </li>
                    <li><a href="<?php echo e(route('home')); ?>#clientReview-sec"> <?php echo e(trans('site.client_reviews')); ?> </a></li>
                    <li><a href="<?php echo e(route('home')); ?>#faq-sec"> <?php echo e(trans('site.faq')); ?> </a></li>
                    <li><a href="<?php echo e(route('home')); ?>#contact-sec"> <?php echo e(trans('site.contact_us')); ?> </a></li>
                    <?php $__currentLoopData = LaravelLocalization::getSupportedLocales(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $localeCode => $properties): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <li>
                        <a class="<?php echo \Illuminate\Support\Arr::toCssClasses([ 'lang-ancor' , 'd-none'=> $localeCode == LaravelLocalization::getCurrentLocale(),
                            ]); ?>" rel="alternate" hreflang="<?php echo e($localeCode); ?>"
                            href="<?php echo e(LaravelLocalization::getLocalizedURL($localeCode, null, [], true)); ?>">
                            <?php echo e($properties['native']); ?>

                        </a>
                    </li>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </ul>
            </div>
            <div class="links-bars-holder">
                <div class="header-links">
                    <a href="<?php echo e($google_play_link); ?>" target="_blank">
                        <i class="fa-brands google fa-google-play icon"></i>
                    </a>
                    <a href="<?php echo e($apple_store_link); ?>" target="_blank">
                        <i class="fa-brands apple fa-apple icon"></i>
                    </a>

                </div>
                <button class="openBtn"><i class="fa-regular fa-bars"></i></button>
            </div>
        </div>
    </div>
</header>
<?php /**PATH D:\Workstation\hoomi\resources\views/site/includes/header.blade.php ENDPATH**/ ?>