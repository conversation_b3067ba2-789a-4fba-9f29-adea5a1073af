<?php

namespace Tasawk\Filament\Resources\Locations\CityResource\Pages;

use Filament\Actions;
use Filament\Resources\Pages\CreateRecord;
use Filament\Resources\Pages\CreateRecord\Concerns\Translatable;
use Tasawk\Filament\Resources\Locations\CityResource;

class CreateCity extends CreateRecord
{
    use Translatable;
    protected static string $resource = CityResource::class;

    protected function getHeaderActions(): array {
        return [
            Actions\LocaleSwitcher::make(),
        ];
    }
    protected function getRedirectUrl(): string {
        return $this->getResource()::getUrl("index");
    }
}
