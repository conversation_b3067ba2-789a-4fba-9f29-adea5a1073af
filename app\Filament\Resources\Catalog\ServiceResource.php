<?php

namespace Tasawk\Filament\Resources\Catalog;

use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\SpatieMediaLibraryFileUpload;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Form;
use Filament\Forms\Get;
use Filament\Infolists\Components\TextEntry;
use Filament\Infolists\Infolist;
use Filament\Notifications\Notification;
use Filament\Resources\Concerns\Translatable;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Actions\Action;
use Filament\Tables\Columns\IconColumn;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Collection;
use Tasawk\Enum\ModelStatus;
use Tasawk\Filament\Resources\Catalog;
use Tasawk\Filament\Resources\Catalog\CategoryResource\RelationManagers\ChildrenRelationManager;
use Tasawk\Models\Catalog\Product;
use Tasawk\Models\Category;
use Tasawk\Models\Service;
use Tasawk\Traits\Filament\HasTranslationLabel;

class ServiceResource extends Resource {
    use Translatable;
    use HasTranslationLabel;

    protected static ?string $model = Service::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';
    protected static ?int $navigationSort = 4;

    public static function form(Form $form): Form {
        return $form
            ->schema([
                Section::make("basic_information")
                    ->schema([
                        Select::make("category_id")
                            ->label(__('forms.fields.category'))
                            ->options(fn(Get $get): Collection => Category::get()->pluck('name', 'id')),
                        TextInput::make('title')->required(),
                        TextInput::make('price')
                            ->formatStateUsing(fn($record)=>$record?->price?->formatByDecimal())
                            ->required()
                            ->suffix(__("forms.suffixes.sar")),


                        Toggle::make('status')
                            ->default(1)
                            ->onColor('success')
                            ->offColor('danger')
                    ])
            ])->columns(1);
    }

    public static function table(Table $table): Table {
        return $table
            ->columns([
                TextColumn::make('id'),
                TextColumn::make('title'),
                TextColumn::make('category.name')
                    ->label(__('forms.fields.category')),
                TextColumn::make('price'),
                IconColumn::make('status')
                    ->boolean()
                    ->action(
                        Action::make('Active')
                            ->label(fn($record): string => $record->status ? __('panel.messages.deactivate') : __('panel.messages.activate'))
                            ->disabled(fn(Model $record): bool => !auth()->user()->can('update', $record))
                            ->requiresConfirmation()
                            ->action(fn($record) => $record->toggleStatus())

                    )
            ])
            ->filters([
                SelectFilter::make('status')
                    ->options(ModelStatus::class),
                SelectFilter::make('category_id')
                    ->options(Category::get()->pluck('name', 'id'))
                    ->label(__('forms.fields.category'))
            ])
            ->actions([
//                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->emptyStateActions([
                Tables\Actions\CreateAction::make(),
            ]);
    }

    static public function infolist(Infolist $infolist): Infolist {
        return $infolist
            ->schema([
                Section::make()
                    ->schema([
                        TextEntry::make('name'),

                        TextEntry::make('status')
                            ->formatStateUsing(fn(string $state): string => $state ? 'Yes' : 'No')
                            ->color(fn(string $state): string => match ($state) {
                                '1' => 'success',
                                '0' => 'danger',
                            })
                    ]),

            ]);
    }

    public static function getRelations(): array {
        return [
        ];
    }

    public static function getPages(): array {
        return [
            'index' => Catalog\ServiceResource\Pages\ListServices::route('/'),
            'create' => Catalog\ServiceResource\Pages\CreateService::route('/create'),
            'edit' => Catalog\ServiceResource\Pages\EditService::route('/{record}/edit'),
        ];
    }

}
