<?php

namespace Tasawk\Filament\Resources\Content\ReportReasonResource\Pages;

use Tasawk\Filament\Resources\Content\ReportReasonResource;
use Filament\Actions;
use Filament\Resources\Pages\CreateRecord;

class CreateReportReason extends CreateRecord
{
    use CreateRecord\Concerns\Translatable;

    protected static string $resource = ReportReasonResource::class;
    protected function getHeaderActions(): array {
        return [
            Actions\LocaleSwitcher::make(),
        ];
    }
    protected function getRedirectUrl(): string {
        return $this->getResource()::getUrl("index");
    }
}
