<?php

namespace Tasawk\Http\Resources\Api\Manager;

use Carbon\Carbon;
use Cknow\Money\Money;
use Illuminate\Http\Resources\Json\JsonResource;

class StatisticsResource extends JsonResource {

    /**
     * Transform the resource into an array.
     *
     * @param \Illuminate\Http\Request $request
     * @return array
     */

    public function toArray($request) {
        return [
            "id" => $this->id,
//            'gateway' => $this->payment_data['gateway'] ?? null,
            'method' => $this->payment_data['method'] ?? null,
            'paid_at' => isset($this->payment_data['paid_at']) ? Carbon::parse($this->payment_data['paid_at'])->timezone('africa/cairo')->format('Y-m-d h:i a') : null,
            'order_total' => $this->total->format(),
            'app_commission' =>Money::parse($this->app_commission)->format(),
            'app_commission_percentage' => $this->app_commission_percentage,
            'worker_dues' =>Money::parse($this->worker_dues)->format(),

        ];
    }

}
