<?php

namespace Tasawk\Api\V1\Manager;

use Cknow\Money\Money;
use Illuminate\Support\Facades\Notification;
use Tasawk\Actions\Customer\BuildCartInstanceAction;
use Tasawk\Api\Core;
use Tasawk\Api\Facade\Api;
use Tasawk\Enum\OrderStatus;
use Tasawk\Enum\ReceiptMethods;
use Tasawk\Http\Requests\Api\Customer\Order\ReportOrderRequest;
use Tasawk\Http\Requests\Api\Manager\ChangeOrderStatusRequest;
use Tasawk\Http\Requests\Api\Manager\PricingOrderRequest;
use Tasawk\Http\Resources\Api\Customer\Cart\CartResource;
use Tasawk\Http\Resources\Api\Manager\Orders\LightOrderResource;
use Tasawk\Http\Resources\Api\Manager\Orders\OrdersResource;
use Tasawk\Http\Resources\Api\Manager\ProductOptionsResource;
use Tasawk\Http\Resources\Api\Manager\ProductResource;
use Tasawk\Http\Resources\Api\Manager\WorkingDayResource;
use Tasawk\Lib\Utils;
use Tasawk\Models\CancellationReason;
use Tasawk\Models\Catalog\Branch\Inventory;
use Tasawk\Models\Catalog\Branch\InventoryOption;
use Tasawk\Models\Catalog\Branch\InventoryOptionValue;
use Tasawk\Models\Manager;
use Tasawk\Models\Order;
use Tasawk\Models\Worker;
use Tasawk\Notifications\Order\NewOrderCreatedNotification;
use Tasawk\Settings\GeneralSettings;


class OrderService {


    public function index() {
        $fleetAccount = auth()->user()->fleetAccount;
        $fleetCategories = $fleetAccount->categories->pluck("id")->toArray();

        $orders = Order::filtered()
            ->when(!$fleetAccount->active,fn($q)=>$q->where('worker_id',auth()->id()))
            ->where('worker_type', $fleetAccount->account_type)
            ->where(fn($q) => $q->whereIn("category_id", $fleetCategories)->orWhereIn("sub_category_id", $fleetCategories))
            ->latest()
            ->paginate();

        if (!auth()->user()->hasEnoughBalanceToAcceptOrders() ) {
            return Api::isOk('orders List', []);
        }

        return Api::isOk('orders List', OrdersResource::collection($orders));
    }

    public function show(Order $order) {
        return Api::isOk('orders List', OrdersResource::make($order));
    }

    public function accept(Order $order) {
        $order->workers()->syncWithoutDetaching([auth()->id()]);
        try {
            Notification::send($order->customer, new \Tasawk\Notifications\Customer\WorkerAcceptOrderNotification($order,auth()->user()));
        } catch (\Exception $e) {

        }
        return Api::isOk('Order accepted');
    }

    public function pricingDetails(PricingOrderRequest $request, Order $order) {
        $cart = BuildCartInstanceAction::run();

        return Api::isOk('cart details', CartResource::make($cart));
    }

    public function pricing(PricingOrderRequest $request, Order $order) {
        abort_if(!$order->canSetPrice(), 400, __("validation.api.cant_set_price"));
        $order->itemsLine()->delete();
        $cart = BuildCartInstanceAction::run();
        $order->update(['status' => OrderStatus::PENDING_FOR_PAY, 'total' => $cart->getTotal()]);
        $cart->saveItemsToOrder($order->id);
        return Api::isOk('cart details', CartResource::make($order->as_cart));
    }

    public function report(ReportOrderRequest $request, Order $order): Core {
        $order->report()->create([...$request->validated(), 'user_id' => auth()->id()]);
        $order->update(['status' => OrderStatus::PROBLEMATIC]);

        return Api::isOk('reported');
    }


}
