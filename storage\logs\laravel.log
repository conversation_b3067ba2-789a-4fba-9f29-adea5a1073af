[2025-05-27 23:40:55] production.ERROR: SQLSTATE[HY000] [1045] Access denied for user 'forge'@'localhost' (using password: YES) (Connection: mysql, SQL: select `name`, `payload` from `settings` where `group` = developer) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 1045): SQLSTATE[HY000] [1045] Access denied for user 'forge'@'localhost' (using password: YES) (Connection: mysql, SQL: select `name`, `payload` from `settings` where `group` = developer) at D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:829)
[stacktrace]
#0 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('select `name`, ...', Array, Object(Closure))
#1 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(431): Illuminate\\Database\\Connection->run('select `name`, ...', Array, Object(Closure))
#2 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2914): Illuminate\\Database\\Connection->select('select `name`, ...', Array, true)
#3 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2902): Illuminate\\Database\\Query\\Builder->runSelect()
#4 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3456): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#5 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2903): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#6 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(739): Illuminate\\Database\\Query\\Builder->get(Array)
#7 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(723): Illuminate\\Database\\Eloquent\\Builder->getModels(Array)
#8 D:\\Workstation\\hoomi\\vendor\\spatie\\laravel-settings\\src\\SettingsRepositories\\DatabaseSettingsRepository.php(28): Illuminate\\Database\\Eloquent\\Builder->get(Array)
#9 D:\\Workstation\\hoomi\\vendor\\spatie\\laravel-settings\\src\\SettingsMapper.php(86): Spatie\\LaravelSettings\\SettingsRepositories\\DatabaseSettingsRepository->getPropertiesInGroup('developer')
#10 D:\\Workstation\\hoomi\\vendor\\spatie\\laravel-settings\\src\\SettingsMapper.php(37): Spatie\\LaravelSettings\\SettingsMapper->fetchProperties('App\\\\Settings\\\\De...', Object(Illuminate\\Support\\Collection))
#11 D:\\Workstation\\hoomi\\vendor\\spatie\\laravel-settings\\src\\Settings.php(277): Spatie\\LaravelSettings\\SettingsMapper->load('App\\\\Settings\\\\De...')
#12 D:\\Workstation\\hoomi\\vendor\\spatie\\laravel-settings\\src\\Settings.php(101): Spatie\\LaravelSettings\\Settings->loadValues()
#13 D:\\Workstation\\hoomi\\app\\Providers\\AppServiceProvider.php(60): Spatie\\LaravelSettings\\Settings->__get('debug_mode')
#14 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Tasawk\\Providers\\AppServiceProvider->boot()
#15 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#16 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#17 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#18 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#19 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1059): Illuminate\\Container\\Container->call(Array)
#20 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1040): Illuminate\\Foundation\\Application->bootProvider(Object(Tasawk\\Providers\\AppServiceProvider))
#21 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(Tasawk\\Providers\\AppServiceProvider), 62)
#22 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1041): array_walk(Array, Object(Closure))
#23 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#24 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(286): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#25 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(447): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#26 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(199): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#27 D:\\Workstation\\hoomi\\artisan(37): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#28 {main}

[previous exception] [object] (PDOException(code: 1045): SQLSTATE[HY000] [1045] Access denied for user 'forge'@'localhost' (using password: YES) at D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php:65)
[stacktrace]
#0 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(65): PDO->__construct('mysql:host=127....', 'forge', '', Array)
#1 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(45): Illuminate\\Database\\Connectors\\Connector->createPdoConnection('mysql:host=127....', 'forge', '', Array)
#2 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\MySqlConnector.php(24): Illuminate\\Database\\Connectors\\Connector->createConnection('mysql:host=127....', Array, Array)
#3 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(184): Illuminate\\Database\\Connectors\\MySqlConnector->connect(Array)
#4 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->Illuminate\\Database\\Connectors\\{closure}()
#5 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1339): call_user_func(Object(Closure))
#6 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1375): Illuminate\\Database\\Connection->getPdo()
#7 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(528): Illuminate\\Database\\Connection->getReadPdo()
#8 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(423): Illuminate\\Database\\Connection->getPdoForSelect(true)
#9 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(816): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select `name`, ...', Array)
#10 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('select `name`, ...', Array, Object(Closure))
#11 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(431): Illuminate\\Database\\Connection->run('select `name`, ...', Array, Object(Closure))
#12 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2914): Illuminate\\Database\\Connection->select('select `name`, ...', Array, true)
#13 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2902): Illuminate\\Database\\Query\\Builder->runSelect()
#14 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3456): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#15 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2903): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#16 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(739): Illuminate\\Database\\Query\\Builder->get(Array)
#17 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(723): Illuminate\\Database\\Eloquent\\Builder->getModels(Array)
#18 D:\\Workstation\\hoomi\\vendor\\spatie\\laravel-settings\\src\\SettingsRepositories\\DatabaseSettingsRepository.php(28): Illuminate\\Database\\Eloquent\\Builder->get(Array)
#19 D:\\Workstation\\hoomi\\vendor\\spatie\\laravel-settings\\src\\SettingsMapper.php(86): Spatie\\LaravelSettings\\SettingsRepositories\\DatabaseSettingsRepository->getPropertiesInGroup('developer')
#20 D:\\Workstation\\hoomi\\vendor\\spatie\\laravel-settings\\src\\SettingsMapper.php(37): Spatie\\LaravelSettings\\SettingsMapper->fetchProperties('App\\\\Settings\\\\De...', Object(Illuminate\\Support\\Collection))
#21 D:\\Workstation\\hoomi\\vendor\\spatie\\laravel-settings\\src\\Settings.php(277): Spatie\\LaravelSettings\\SettingsMapper->load('App\\\\Settings\\\\De...')
#22 D:\\Workstation\\hoomi\\vendor\\spatie\\laravel-settings\\src\\Settings.php(101): Spatie\\LaravelSettings\\Settings->loadValues()
#23 D:\\Workstation\\hoomi\\app\\Providers\\AppServiceProvider.php(60): Spatie\\LaravelSettings\\Settings->__get('debug_mode')
#24 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Tasawk\\Providers\\AppServiceProvider->boot()
#25 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#26 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#27 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#28 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#29 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1059): Illuminate\\Container\\Container->call(Array)
#30 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1040): Illuminate\\Foundation\\Application->bootProvider(Object(Tasawk\\Providers\\AppServiceProvider))
#31 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(Tasawk\\Providers\\AppServiceProvider), 62)
#32 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1041): array_walk(Array, Object(Closure))
#33 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#34 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(286): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#35 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(447): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#36 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(199): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#37 D:\\Workstation\\hoomi\\artisan(37): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#38 {main}
"} 
[2025-05-28 09:20:20] production.ERROR: SQLSTATE[HY000] [1045] Access denied for user 'forge'@'localhost' (using password: YES) (Connection: mysql, SQL: select `name`, `payload` from `settings` where `group` = developer) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 1045): SQLSTATE[HY000] [1045] Access denied for user 'forge'@'localhost' (using password: YES) (Connection: mysql, SQL: select `name`, `payload` from `settings` where `group` = developer) at D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:829)
[stacktrace]
#0 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('select `name`, ...', Array, Object(Closure))
#1 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(431): Illuminate\\Database\\Connection->run('select `name`, ...', Array, Object(Closure))
#2 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2914): Illuminate\\Database\\Connection->select('select `name`, ...', Array, true)
#3 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2902): Illuminate\\Database\\Query\\Builder->runSelect()
#4 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3456): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#5 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2903): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#6 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(739): Illuminate\\Database\\Query\\Builder->get(Array)
#7 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(723): Illuminate\\Database\\Eloquent\\Builder->getModels(Array)
#8 D:\\Workstation\\hoomi\\vendor\\spatie\\laravel-settings\\src\\SettingsRepositories\\DatabaseSettingsRepository.php(28): Illuminate\\Database\\Eloquent\\Builder->get(Array)
#9 D:\\Workstation\\hoomi\\vendor\\spatie\\laravel-settings\\src\\SettingsMapper.php(86): Spatie\\LaravelSettings\\SettingsRepositories\\DatabaseSettingsRepository->getPropertiesInGroup('developer')
#10 D:\\Workstation\\hoomi\\vendor\\spatie\\laravel-settings\\src\\SettingsMapper.php(37): Spatie\\LaravelSettings\\SettingsMapper->fetchProperties('App\\\\Settings\\\\De...', Object(Illuminate\\Support\\Collection))
#11 D:\\Workstation\\hoomi\\vendor\\spatie\\laravel-settings\\src\\Settings.php(277): Spatie\\LaravelSettings\\SettingsMapper->load('App\\\\Settings\\\\De...')
#12 D:\\Workstation\\hoomi\\vendor\\spatie\\laravel-settings\\src\\Settings.php(101): Spatie\\LaravelSettings\\Settings->loadValues()
#13 D:\\Workstation\\hoomi\\app\\Providers\\AppServiceProvider.php(60): Spatie\\LaravelSettings\\Settings->__get('debug_mode')
#14 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Tasawk\\Providers\\AppServiceProvider->boot()
#15 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#16 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#17 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#18 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#19 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1059): Illuminate\\Container\\Container->call(Array)
#20 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1040): Illuminate\\Foundation\\Application->bootProvider(Object(Tasawk\\Providers\\AppServiceProvider))
#21 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(Tasawk\\Providers\\AppServiceProvider), 63)
#22 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1041): array_walk(Array, Object(Closure))
#23 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#24 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(286): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#25 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(447): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#26 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(199): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#27 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#28 {main}

[previous exception] [object] (PDOException(code: 1045): SQLSTATE[HY000] [1045] Access denied for user 'forge'@'localhost' (using password: YES) at D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php:65)
[stacktrace]
#0 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(65): PDO->__construct('mysql:host=127....', 'forge', '', Array)
#1 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(45): Illuminate\\Database\\Connectors\\Connector->createPdoConnection('mysql:host=127....', 'forge', '', Array)
#2 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\MySqlConnector.php(24): Illuminate\\Database\\Connectors\\Connector->createConnection('mysql:host=127....', Array, Array)
#3 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(184): Illuminate\\Database\\Connectors\\MySqlConnector->connect(Array)
#4 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->Illuminate\\Database\\Connectors\\{closure}()
#5 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1339): call_user_func(Object(Closure))
#6 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1375): Illuminate\\Database\\Connection->getPdo()
#7 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(528): Illuminate\\Database\\Connection->getReadPdo()
#8 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(423): Illuminate\\Database\\Connection->getPdoForSelect(true)
#9 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(816): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select `name`, ...', Array)
#10 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('select `name`, ...', Array, Object(Closure))
#11 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(431): Illuminate\\Database\\Connection->run('select `name`, ...', Array, Object(Closure))
#12 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2914): Illuminate\\Database\\Connection->select('select `name`, ...', Array, true)
#13 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2902): Illuminate\\Database\\Query\\Builder->runSelect()
#14 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3456): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#15 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2903): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#16 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(739): Illuminate\\Database\\Query\\Builder->get(Array)
#17 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(723): Illuminate\\Database\\Eloquent\\Builder->getModels(Array)
#18 D:\\Workstation\\hoomi\\vendor\\spatie\\laravel-settings\\src\\SettingsRepositories\\DatabaseSettingsRepository.php(28): Illuminate\\Database\\Eloquent\\Builder->get(Array)
#19 D:\\Workstation\\hoomi\\vendor\\spatie\\laravel-settings\\src\\SettingsMapper.php(86): Spatie\\LaravelSettings\\SettingsRepositories\\DatabaseSettingsRepository->getPropertiesInGroup('developer')
#20 D:\\Workstation\\hoomi\\vendor\\spatie\\laravel-settings\\src\\SettingsMapper.php(37): Spatie\\LaravelSettings\\SettingsMapper->fetchProperties('App\\\\Settings\\\\De...', Object(Illuminate\\Support\\Collection))
#21 D:\\Workstation\\hoomi\\vendor\\spatie\\laravel-settings\\src\\Settings.php(277): Spatie\\LaravelSettings\\SettingsMapper->load('App\\\\Settings\\\\De...')
#22 D:\\Workstation\\hoomi\\vendor\\spatie\\laravel-settings\\src\\Settings.php(101): Spatie\\LaravelSettings\\Settings->loadValues()
#23 D:\\Workstation\\hoomi\\app\\Providers\\AppServiceProvider.php(60): Spatie\\LaravelSettings\\Settings->__get('debug_mode')
#24 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Tasawk\\Providers\\AppServiceProvider->boot()
#25 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#26 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#27 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#28 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#29 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1059): Illuminate\\Container\\Container->call(Array)
#30 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1040): Illuminate\\Foundation\\Application->bootProvider(Object(Tasawk\\Providers\\AppServiceProvider))
#31 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(Tasawk\\Providers\\AppServiceProvider), 63)
#32 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1041): array_walk(Array, Object(Closure))
#33 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#34 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(286): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#35 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(447): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#36 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(199): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#37 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#38 {main}
"} 
[2025-05-28 09:20:20] production.ERROR: SQLSTATE[HY000] [1045] Access denied for user 'forge'@'localhost' (using password: YES) (Connection: mysql, SQL: select `name`, `payload` from `settings` where `group` = developer) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 1045): SQLSTATE[HY000] [1045] Access denied for user 'forge'@'localhost' (using password: YES) (Connection: mysql, SQL: select `name`, `payload` from `settings` where `group` = developer) at D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:829)
[stacktrace]
#0 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('select `name`, ...', Array, Object(Closure))
#1 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(431): Illuminate\\Database\\Connection->run('select `name`, ...', Array, Object(Closure))
#2 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2914): Illuminate\\Database\\Connection->select('select `name`, ...', Array, true)
#3 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2902): Illuminate\\Database\\Query\\Builder->runSelect()
#4 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3456): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#5 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2903): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#6 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(739): Illuminate\\Database\\Query\\Builder->get(Array)
#7 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(723): Illuminate\\Database\\Eloquent\\Builder->getModels(Array)
#8 D:\\Workstation\\hoomi\\vendor\\spatie\\laravel-settings\\src\\SettingsRepositories\\DatabaseSettingsRepository.php(28): Illuminate\\Database\\Eloquent\\Builder->get(Array)
#9 D:\\Workstation\\hoomi\\vendor\\spatie\\laravel-settings\\src\\SettingsMapper.php(86): Spatie\\LaravelSettings\\SettingsRepositories\\DatabaseSettingsRepository->getPropertiesInGroup('developer')
#10 D:\\Workstation\\hoomi\\vendor\\spatie\\laravel-settings\\src\\SettingsMapper.php(37): Spatie\\LaravelSettings\\SettingsMapper->fetchProperties('App\\\\Settings\\\\De...', Object(Illuminate\\Support\\Collection))
#11 D:\\Workstation\\hoomi\\vendor\\spatie\\laravel-settings\\src\\Settings.php(277): Spatie\\LaravelSettings\\SettingsMapper->load('App\\\\Settings\\\\De...')
#12 D:\\Workstation\\hoomi\\vendor\\spatie\\laravel-settings\\src\\Settings.php(101): Spatie\\LaravelSettings\\Settings->loadValues()
#13 D:\\Workstation\\hoomi\\app\\Providers\\AppServiceProvider.php(60): Spatie\\LaravelSettings\\Settings->__get('debug_mode')
#14 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Tasawk\\Providers\\AppServiceProvider->boot()
#15 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#16 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#17 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#18 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#19 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1059): Illuminate\\Container\\Container->call(Array)
#20 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1040): Illuminate\\Foundation\\Application->bootProvider(Object(Tasawk\\Providers\\AppServiceProvider))
#21 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(Tasawk\\Providers\\AppServiceProvider), 63)
#22 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1041): array_walk(Array, Object(Closure))
#23 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#24 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(286): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#25 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(447): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#26 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(199): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#27 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#28 {main}

[previous exception] [object] (PDOException(code: 1045): SQLSTATE[HY000] [1045] Access denied for user 'forge'@'localhost' (using password: YES) at D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php:65)
[stacktrace]
#0 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(65): PDO->__construct('mysql:host=127....', 'forge', '', Array)
#1 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(45): Illuminate\\Database\\Connectors\\Connector->createPdoConnection('mysql:host=127....', 'forge', '', Array)
#2 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\MySqlConnector.php(24): Illuminate\\Database\\Connectors\\Connector->createConnection('mysql:host=127....', Array, Array)
#3 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(184): Illuminate\\Database\\Connectors\\MySqlConnector->connect(Array)
#4 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->Illuminate\\Database\\Connectors\\{closure}()
#5 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1339): call_user_func(Object(Closure))
#6 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1375): Illuminate\\Database\\Connection->getPdo()
#7 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(528): Illuminate\\Database\\Connection->getReadPdo()
#8 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(423): Illuminate\\Database\\Connection->getPdoForSelect(true)
#9 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(816): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select `name`, ...', Array)
#10 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('select `name`, ...', Array, Object(Closure))
#11 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(431): Illuminate\\Database\\Connection->run('select `name`, ...', Array, Object(Closure))
#12 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2914): Illuminate\\Database\\Connection->select('select `name`, ...', Array, true)
#13 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2902): Illuminate\\Database\\Query\\Builder->runSelect()
#14 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3456): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#15 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2903): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#16 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(739): Illuminate\\Database\\Query\\Builder->get(Array)
#17 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(723): Illuminate\\Database\\Eloquent\\Builder->getModels(Array)
#18 D:\\Workstation\\hoomi\\vendor\\spatie\\laravel-settings\\src\\SettingsRepositories\\DatabaseSettingsRepository.php(28): Illuminate\\Database\\Eloquent\\Builder->get(Array)
#19 D:\\Workstation\\hoomi\\vendor\\spatie\\laravel-settings\\src\\SettingsMapper.php(86): Spatie\\LaravelSettings\\SettingsRepositories\\DatabaseSettingsRepository->getPropertiesInGroup('developer')
#20 D:\\Workstation\\hoomi\\vendor\\spatie\\laravel-settings\\src\\SettingsMapper.php(37): Spatie\\LaravelSettings\\SettingsMapper->fetchProperties('App\\\\Settings\\\\De...', Object(Illuminate\\Support\\Collection))
#21 D:\\Workstation\\hoomi\\vendor\\spatie\\laravel-settings\\src\\Settings.php(277): Spatie\\LaravelSettings\\SettingsMapper->load('App\\\\Settings\\\\De...')
#22 D:\\Workstation\\hoomi\\vendor\\spatie\\laravel-settings\\src\\Settings.php(101): Spatie\\LaravelSettings\\Settings->loadValues()
#23 D:\\Workstation\\hoomi\\app\\Providers\\AppServiceProvider.php(60): Spatie\\LaravelSettings\\Settings->__get('debug_mode')
#24 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Tasawk\\Providers\\AppServiceProvider->boot()
#25 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#26 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#27 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#28 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#29 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1059): Illuminate\\Container\\Container->call(Array)
#30 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1040): Illuminate\\Foundation\\Application->bootProvider(Object(Tasawk\\Providers\\AppServiceProvider))
#31 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(Tasawk\\Providers\\AppServiceProvider), 63)
#32 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1041): array_walk(Array, Object(Closure))
#33 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#34 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(286): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#35 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(447): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#36 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(199): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#37 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#38 {main}
"} 
[2025-05-28 09:20:20] production.ERROR: SQLSTATE[HY000] [1045] Access denied for user 'forge'@'localhost' (using password: YES) (Connection: mysql, SQL: select `name`, `payload` from `settings` where `group` = developer) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 1045): SQLSTATE[HY000] [1045] Access denied for user 'forge'@'localhost' (using password: YES) (Connection: mysql, SQL: select `name`, `payload` from `settings` where `group` = developer) at D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:829)
[stacktrace]
#0 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('select `name`, ...', Array, Object(Closure))
#1 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(431): Illuminate\\Database\\Connection->run('select `name`, ...', Array, Object(Closure))
#2 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2914): Illuminate\\Database\\Connection->select('select `name`, ...', Array, true)
#3 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2902): Illuminate\\Database\\Query\\Builder->runSelect()
#4 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3456): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#5 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2903): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#6 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(739): Illuminate\\Database\\Query\\Builder->get(Array)
#7 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(723): Illuminate\\Database\\Eloquent\\Builder->getModels(Array)
#8 D:\\Workstation\\hoomi\\vendor\\spatie\\laravel-settings\\src\\SettingsRepositories\\DatabaseSettingsRepository.php(28): Illuminate\\Database\\Eloquent\\Builder->get(Array)
#9 D:\\Workstation\\hoomi\\vendor\\spatie\\laravel-settings\\src\\SettingsMapper.php(86): Spatie\\LaravelSettings\\SettingsRepositories\\DatabaseSettingsRepository->getPropertiesInGroup('developer')
#10 D:\\Workstation\\hoomi\\vendor\\spatie\\laravel-settings\\src\\SettingsMapper.php(37): Spatie\\LaravelSettings\\SettingsMapper->fetchProperties('App\\\\Settings\\\\De...', Object(Illuminate\\Support\\Collection))
#11 D:\\Workstation\\hoomi\\vendor\\spatie\\laravel-settings\\src\\Settings.php(277): Spatie\\LaravelSettings\\SettingsMapper->load('App\\\\Settings\\\\De...')
#12 D:\\Workstation\\hoomi\\vendor\\spatie\\laravel-settings\\src\\Settings.php(101): Spatie\\LaravelSettings\\Settings->loadValues()
#13 D:\\Workstation\\hoomi\\app\\Providers\\AppServiceProvider.php(60): Spatie\\LaravelSettings\\Settings->__get('debug_mode')
#14 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Tasawk\\Providers\\AppServiceProvider->boot()
#15 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#16 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#17 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#18 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#19 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1059): Illuminate\\Container\\Container->call(Array)
#20 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1040): Illuminate\\Foundation\\Application->bootProvider(Object(Tasawk\\Providers\\AppServiceProvider))
#21 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(Tasawk\\Providers\\AppServiceProvider), 63)
#22 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1041): array_walk(Array, Object(Closure))
#23 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#24 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(286): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#25 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(447): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#26 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(199): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#27 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#28 {main}

[previous exception] [object] (PDOException(code: 1045): SQLSTATE[HY000] [1045] Access denied for user 'forge'@'localhost' (using password: YES) at D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php:65)
[stacktrace]
#0 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(65): PDO->__construct('mysql:host=127....', 'forge', '', Array)
#1 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(45): Illuminate\\Database\\Connectors\\Connector->createPdoConnection('mysql:host=127....', 'forge', '', Array)
#2 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\MySqlConnector.php(24): Illuminate\\Database\\Connectors\\Connector->createConnection('mysql:host=127....', Array, Array)
#3 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(184): Illuminate\\Database\\Connectors\\MySqlConnector->connect(Array)
#4 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->Illuminate\\Database\\Connectors\\{closure}()
#5 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1339): call_user_func(Object(Closure))
#6 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1375): Illuminate\\Database\\Connection->getPdo()
#7 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(528): Illuminate\\Database\\Connection->getReadPdo()
#8 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(423): Illuminate\\Database\\Connection->getPdoForSelect(true)
#9 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(816): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select `name`, ...', Array)
#10 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('select `name`, ...', Array, Object(Closure))
#11 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(431): Illuminate\\Database\\Connection->run('select `name`, ...', Array, Object(Closure))
#12 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2914): Illuminate\\Database\\Connection->select('select `name`, ...', Array, true)
#13 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2902): Illuminate\\Database\\Query\\Builder->runSelect()
#14 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3456): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#15 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2903): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#16 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(739): Illuminate\\Database\\Query\\Builder->get(Array)
#17 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(723): Illuminate\\Database\\Eloquent\\Builder->getModels(Array)
#18 D:\\Workstation\\hoomi\\vendor\\spatie\\laravel-settings\\src\\SettingsRepositories\\DatabaseSettingsRepository.php(28): Illuminate\\Database\\Eloquent\\Builder->get(Array)
#19 D:\\Workstation\\hoomi\\vendor\\spatie\\laravel-settings\\src\\SettingsMapper.php(86): Spatie\\LaravelSettings\\SettingsRepositories\\DatabaseSettingsRepository->getPropertiesInGroup('developer')
#20 D:\\Workstation\\hoomi\\vendor\\spatie\\laravel-settings\\src\\SettingsMapper.php(37): Spatie\\LaravelSettings\\SettingsMapper->fetchProperties('App\\\\Settings\\\\De...', Object(Illuminate\\Support\\Collection))
#21 D:\\Workstation\\hoomi\\vendor\\spatie\\laravel-settings\\src\\Settings.php(277): Spatie\\LaravelSettings\\SettingsMapper->load('App\\\\Settings\\\\De...')
#22 D:\\Workstation\\hoomi\\vendor\\spatie\\laravel-settings\\src\\Settings.php(101): Spatie\\LaravelSettings\\Settings->loadValues()
#23 D:\\Workstation\\hoomi\\app\\Providers\\AppServiceProvider.php(60): Spatie\\LaravelSettings\\Settings->__get('debug_mode')
#24 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Tasawk\\Providers\\AppServiceProvider->boot()
#25 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#26 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#27 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#28 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#29 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1059): Illuminate\\Container\\Container->call(Array)
#30 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1040): Illuminate\\Foundation\\Application->bootProvider(Object(Tasawk\\Providers\\AppServiceProvider))
#31 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(Tasawk\\Providers\\AppServiceProvider), 63)
#32 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1041): array_walk(Array, Object(Closure))
#33 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#34 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(286): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#35 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(447): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#36 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(199): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#37 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#38 {main}
"} 
[2025-05-28 09:21:16] production.ERROR: SQLSTATE[HY000] [1045] Access denied for user 'forge'@'localhost' (using password: YES) (Connection: mysql, SQL: select `name`, `payload` from `settings` where `group` = developer) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 1045): SQLSTATE[HY000] [1045] Access denied for user 'forge'@'localhost' (using password: YES) (Connection: mysql, SQL: select `name`, `payload` from `settings` where `group` = developer) at D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:829)
[stacktrace]
#0 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('select `name`, ...', Array, Object(Closure))
#1 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(431): Illuminate\\Database\\Connection->run('select `name`, ...', Array, Object(Closure))
#2 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2914): Illuminate\\Database\\Connection->select('select `name`, ...', Array, true)
#3 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2902): Illuminate\\Database\\Query\\Builder->runSelect()
#4 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3456): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#5 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2903): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#6 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(739): Illuminate\\Database\\Query\\Builder->get(Array)
#7 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(723): Illuminate\\Database\\Eloquent\\Builder->getModels(Array)
#8 D:\\Workstation\\hoomi\\vendor\\spatie\\laravel-settings\\src\\SettingsRepositories\\DatabaseSettingsRepository.php(28): Illuminate\\Database\\Eloquent\\Builder->get(Array)
#9 D:\\Workstation\\hoomi\\vendor\\spatie\\laravel-settings\\src\\SettingsMapper.php(86): Spatie\\LaravelSettings\\SettingsRepositories\\DatabaseSettingsRepository->getPropertiesInGroup('developer')
#10 D:\\Workstation\\hoomi\\vendor\\spatie\\laravel-settings\\src\\SettingsMapper.php(37): Spatie\\LaravelSettings\\SettingsMapper->fetchProperties('App\\\\Settings\\\\De...', Object(Illuminate\\Support\\Collection))
#11 D:\\Workstation\\hoomi\\vendor\\spatie\\laravel-settings\\src\\Settings.php(277): Spatie\\LaravelSettings\\SettingsMapper->load('App\\\\Settings\\\\De...')
#12 D:\\Workstation\\hoomi\\vendor\\spatie\\laravel-settings\\src\\Settings.php(101): Spatie\\LaravelSettings\\Settings->loadValues()
#13 D:\\Workstation\\hoomi\\app\\Providers\\AppServiceProvider.php(60): Spatie\\LaravelSettings\\Settings->__get('debug_mode')
#14 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Tasawk\\Providers\\AppServiceProvider->boot()
#15 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#16 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#17 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#18 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#19 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1059): Illuminate\\Container\\Container->call(Array)
#20 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1040): Illuminate\\Foundation\\Application->bootProvider(Object(Tasawk\\Providers\\AppServiceProvider))
#21 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(Tasawk\\Providers\\AppServiceProvider), 63)
#22 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1041): array_walk(Array, Object(Closure))
#23 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#24 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(286): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#25 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(447): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#26 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(199): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#27 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#28 {main}

[previous exception] [object] (PDOException(code: 1045): SQLSTATE[HY000] [1045] Access denied for user 'forge'@'localhost' (using password: YES) at D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php:65)
[stacktrace]
#0 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(65): PDO->__construct('mysql:host=127....', 'forge', '', Array)
#1 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(45): Illuminate\\Database\\Connectors\\Connector->createPdoConnection('mysql:host=127....', 'forge', '', Array)
#2 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\MySqlConnector.php(24): Illuminate\\Database\\Connectors\\Connector->createConnection('mysql:host=127....', Array, Array)
#3 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(184): Illuminate\\Database\\Connectors\\MySqlConnector->connect(Array)
#4 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->Illuminate\\Database\\Connectors\\{closure}()
#5 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1339): call_user_func(Object(Closure))
#6 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1375): Illuminate\\Database\\Connection->getPdo()
#7 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(528): Illuminate\\Database\\Connection->getReadPdo()
#8 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(423): Illuminate\\Database\\Connection->getPdoForSelect(true)
#9 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(816): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select `name`, ...', Array)
#10 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('select `name`, ...', Array, Object(Closure))
#11 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(431): Illuminate\\Database\\Connection->run('select `name`, ...', Array, Object(Closure))
#12 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2914): Illuminate\\Database\\Connection->select('select `name`, ...', Array, true)
#13 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2902): Illuminate\\Database\\Query\\Builder->runSelect()
#14 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3456): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#15 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2903): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#16 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(739): Illuminate\\Database\\Query\\Builder->get(Array)
#17 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(723): Illuminate\\Database\\Eloquent\\Builder->getModels(Array)
#18 D:\\Workstation\\hoomi\\vendor\\spatie\\laravel-settings\\src\\SettingsRepositories\\DatabaseSettingsRepository.php(28): Illuminate\\Database\\Eloquent\\Builder->get(Array)
#19 D:\\Workstation\\hoomi\\vendor\\spatie\\laravel-settings\\src\\SettingsMapper.php(86): Spatie\\LaravelSettings\\SettingsRepositories\\DatabaseSettingsRepository->getPropertiesInGroup('developer')
#20 D:\\Workstation\\hoomi\\vendor\\spatie\\laravel-settings\\src\\SettingsMapper.php(37): Spatie\\LaravelSettings\\SettingsMapper->fetchProperties('App\\\\Settings\\\\De...', Object(Illuminate\\Support\\Collection))
#21 D:\\Workstation\\hoomi\\vendor\\spatie\\laravel-settings\\src\\Settings.php(277): Spatie\\LaravelSettings\\SettingsMapper->load('App\\\\Settings\\\\De...')
#22 D:\\Workstation\\hoomi\\vendor\\spatie\\laravel-settings\\src\\Settings.php(101): Spatie\\LaravelSettings\\Settings->loadValues()
#23 D:\\Workstation\\hoomi\\app\\Providers\\AppServiceProvider.php(60): Spatie\\LaravelSettings\\Settings->__get('debug_mode')
#24 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Tasawk\\Providers\\AppServiceProvider->boot()
#25 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#26 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#27 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#28 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#29 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1059): Illuminate\\Container\\Container->call(Array)
#30 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1040): Illuminate\\Foundation\\Application->bootProvider(Object(Tasawk\\Providers\\AppServiceProvider))
#31 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(Tasawk\\Providers\\AppServiceProvider), 63)
#32 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1041): array_walk(Array, Object(Closure))
#33 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#34 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(286): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#35 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(447): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#36 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(199): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#37 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#38 {main}
"} 
[2025-05-28 09:22:12] production.ERROR: SQLSTATE[HY000] [1045] Access denied for user 'forge'@'localhost' (using password: YES) (Connection: mysql, SQL: select `name`, `payload` from `settings` where `group` = developer) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 1045): SQLSTATE[HY000] [1045] Access denied for user 'forge'@'localhost' (using password: YES) (Connection: mysql, SQL: select `name`, `payload` from `settings` where `group` = developer) at D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:829)
[stacktrace]
#0 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('select `name`, ...', Array, Object(Closure))
#1 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(431): Illuminate\\Database\\Connection->run('select `name`, ...', Array, Object(Closure))
#2 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2914): Illuminate\\Database\\Connection->select('select `name`, ...', Array, true)
#3 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2902): Illuminate\\Database\\Query\\Builder->runSelect()
#4 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3456): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#5 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2903): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#6 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(739): Illuminate\\Database\\Query\\Builder->get(Array)
#7 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(723): Illuminate\\Database\\Eloquent\\Builder->getModels(Array)
#8 D:\\Workstation\\hoomi\\vendor\\spatie\\laravel-settings\\src\\SettingsRepositories\\DatabaseSettingsRepository.php(28): Illuminate\\Database\\Eloquent\\Builder->get(Array)
#9 D:\\Workstation\\hoomi\\vendor\\spatie\\laravel-settings\\src\\SettingsMapper.php(86): Spatie\\LaravelSettings\\SettingsRepositories\\DatabaseSettingsRepository->getPropertiesInGroup('developer')
#10 D:\\Workstation\\hoomi\\vendor\\spatie\\laravel-settings\\src\\SettingsMapper.php(37): Spatie\\LaravelSettings\\SettingsMapper->fetchProperties('App\\\\Settings\\\\De...', Object(Illuminate\\Support\\Collection))
#11 D:\\Workstation\\hoomi\\vendor\\spatie\\laravel-settings\\src\\Settings.php(277): Spatie\\LaravelSettings\\SettingsMapper->load('App\\\\Settings\\\\De...')
#12 D:\\Workstation\\hoomi\\vendor\\spatie\\laravel-settings\\src\\Settings.php(101): Spatie\\LaravelSettings\\Settings->loadValues()
#13 D:\\Workstation\\hoomi\\app\\Providers\\AppServiceProvider.php(60): Spatie\\LaravelSettings\\Settings->__get('debug_mode')
#14 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Tasawk\\Providers\\AppServiceProvider->boot()
#15 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#16 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#17 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#18 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#19 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1059): Illuminate\\Container\\Container->call(Array)
#20 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1040): Illuminate\\Foundation\\Application->bootProvider(Object(Tasawk\\Providers\\AppServiceProvider))
#21 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(Tasawk\\Providers\\AppServiceProvider), 63)
#22 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1041): array_walk(Array, Object(Closure))
#23 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#24 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(286): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#25 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(447): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#26 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(199): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#27 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#28 {main}

[previous exception] [object] (PDOException(code: 1045): SQLSTATE[HY000] [1045] Access denied for user 'forge'@'localhost' (using password: YES) at D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php:65)
[stacktrace]
#0 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(65): PDO->__construct('mysql:host=127....', 'forge', '', Array)
#1 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(45): Illuminate\\Database\\Connectors\\Connector->createPdoConnection('mysql:host=127....', 'forge', '', Array)
#2 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\MySqlConnector.php(24): Illuminate\\Database\\Connectors\\Connector->createConnection('mysql:host=127....', Array, Array)
#3 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(184): Illuminate\\Database\\Connectors\\MySqlConnector->connect(Array)
#4 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->Illuminate\\Database\\Connectors\\{closure}()
#5 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1339): call_user_func(Object(Closure))
#6 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1375): Illuminate\\Database\\Connection->getPdo()
#7 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(528): Illuminate\\Database\\Connection->getReadPdo()
#8 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(423): Illuminate\\Database\\Connection->getPdoForSelect(true)
#9 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(816): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select `name`, ...', Array)
#10 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('select `name`, ...', Array, Object(Closure))
#11 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(431): Illuminate\\Database\\Connection->run('select `name`, ...', Array, Object(Closure))
#12 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2914): Illuminate\\Database\\Connection->select('select `name`, ...', Array, true)
#13 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2902): Illuminate\\Database\\Query\\Builder->runSelect()
#14 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3456): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#15 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2903): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#16 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(739): Illuminate\\Database\\Query\\Builder->get(Array)
#17 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(723): Illuminate\\Database\\Eloquent\\Builder->getModels(Array)
#18 D:\\Workstation\\hoomi\\vendor\\spatie\\laravel-settings\\src\\SettingsRepositories\\DatabaseSettingsRepository.php(28): Illuminate\\Database\\Eloquent\\Builder->get(Array)
#19 D:\\Workstation\\hoomi\\vendor\\spatie\\laravel-settings\\src\\SettingsMapper.php(86): Spatie\\LaravelSettings\\SettingsRepositories\\DatabaseSettingsRepository->getPropertiesInGroup('developer')
#20 D:\\Workstation\\hoomi\\vendor\\spatie\\laravel-settings\\src\\SettingsMapper.php(37): Spatie\\LaravelSettings\\SettingsMapper->fetchProperties('App\\\\Settings\\\\De...', Object(Illuminate\\Support\\Collection))
#21 D:\\Workstation\\hoomi\\vendor\\spatie\\laravel-settings\\src\\Settings.php(277): Spatie\\LaravelSettings\\SettingsMapper->load('App\\\\Settings\\\\De...')
#22 D:\\Workstation\\hoomi\\vendor\\spatie\\laravel-settings\\src\\Settings.php(101): Spatie\\LaravelSettings\\Settings->loadValues()
#23 D:\\Workstation\\hoomi\\app\\Providers\\AppServiceProvider.php(60): Spatie\\LaravelSettings\\Settings->__get('debug_mode')
#24 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Tasawk\\Providers\\AppServiceProvider->boot()
#25 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#26 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#27 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#28 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#29 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1059): Illuminate\\Container\\Container->call(Array)
#30 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1040): Illuminate\\Foundation\\Application->bootProvider(Object(Tasawk\\Providers\\AppServiceProvider))
#31 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(Tasawk\\Providers\\AppServiceProvider), 63)
#32 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1041): array_walk(Array, Object(Closure))
#33 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#34 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(286): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#35 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(447): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#36 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(199): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#37 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#38 {main}
"} 
[2025-05-28 09:22:12] production.ERROR: SQLSTATE[HY000] [1045] Access denied for user 'forge'@'localhost' (using password: YES) (Connection: mysql, SQL: select `name`, `payload` from `settings` where `group` = developer) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 1045): SQLSTATE[HY000] [1045] Access denied for user 'forge'@'localhost' (using password: YES) (Connection: mysql, SQL: select `name`, `payload` from `settings` where `group` = developer) at D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:829)
[stacktrace]
#0 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('select `name`, ...', Array, Object(Closure))
#1 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(431): Illuminate\\Database\\Connection->run('select `name`, ...', Array, Object(Closure))
#2 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2914): Illuminate\\Database\\Connection->select('select `name`, ...', Array, true)
#3 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2902): Illuminate\\Database\\Query\\Builder->runSelect()
#4 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3456): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#5 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2903): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#6 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(739): Illuminate\\Database\\Query\\Builder->get(Array)
#7 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(723): Illuminate\\Database\\Eloquent\\Builder->getModels(Array)
#8 D:\\Workstation\\hoomi\\vendor\\spatie\\laravel-settings\\src\\SettingsRepositories\\DatabaseSettingsRepository.php(28): Illuminate\\Database\\Eloquent\\Builder->get(Array)
#9 D:\\Workstation\\hoomi\\vendor\\spatie\\laravel-settings\\src\\SettingsMapper.php(86): Spatie\\LaravelSettings\\SettingsRepositories\\DatabaseSettingsRepository->getPropertiesInGroup('developer')
#10 D:\\Workstation\\hoomi\\vendor\\spatie\\laravel-settings\\src\\SettingsMapper.php(37): Spatie\\LaravelSettings\\SettingsMapper->fetchProperties('App\\\\Settings\\\\De...', Object(Illuminate\\Support\\Collection))
#11 D:\\Workstation\\hoomi\\vendor\\spatie\\laravel-settings\\src\\Settings.php(277): Spatie\\LaravelSettings\\SettingsMapper->load('App\\\\Settings\\\\De...')
#12 D:\\Workstation\\hoomi\\vendor\\spatie\\laravel-settings\\src\\Settings.php(101): Spatie\\LaravelSettings\\Settings->loadValues()
#13 D:\\Workstation\\hoomi\\app\\Providers\\AppServiceProvider.php(60): Spatie\\LaravelSettings\\Settings->__get('debug_mode')
#14 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Tasawk\\Providers\\AppServiceProvider->boot()
#15 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#16 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#17 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#18 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#19 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1059): Illuminate\\Container\\Container->call(Array)
#20 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1040): Illuminate\\Foundation\\Application->bootProvider(Object(Tasawk\\Providers\\AppServiceProvider))
#21 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(Tasawk\\Providers\\AppServiceProvider), 63)
#22 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1041): array_walk(Array, Object(Closure))
#23 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#24 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(286): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#25 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(447): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#26 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(199): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#27 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#28 {main}

[previous exception] [object] (PDOException(code: 1045): SQLSTATE[HY000] [1045] Access denied for user 'forge'@'localhost' (using password: YES) at D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php:65)
[stacktrace]
#0 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(65): PDO->__construct('mysql:host=127....', 'forge', '', Array)
#1 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(45): Illuminate\\Database\\Connectors\\Connector->createPdoConnection('mysql:host=127....', 'forge', '', Array)
#2 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\MySqlConnector.php(24): Illuminate\\Database\\Connectors\\Connector->createConnection('mysql:host=127....', Array, Array)
#3 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(184): Illuminate\\Database\\Connectors\\MySqlConnector->connect(Array)
#4 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->Illuminate\\Database\\Connectors\\{closure}()
#5 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1339): call_user_func(Object(Closure))
#6 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1375): Illuminate\\Database\\Connection->getPdo()
#7 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(528): Illuminate\\Database\\Connection->getReadPdo()
#8 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(423): Illuminate\\Database\\Connection->getPdoForSelect(true)
#9 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(816): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select `name`, ...', Array)
#10 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('select `name`, ...', Array, Object(Closure))
#11 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(431): Illuminate\\Database\\Connection->run('select `name`, ...', Array, Object(Closure))
#12 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2914): Illuminate\\Database\\Connection->select('select `name`, ...', Array, true)
#13 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2902): Illuminate\\Database\\Query\\Builder->runSelect()
#14 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3456): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#15 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2903): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#16 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(739): Illuminate\\Database\\Query\\Builder->get(Array)
#17 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(723): Illuminate\\Database\\Eloquent\\Builder->getModels(Array)
#18 D:\\Workstation\\hoomi\\vendor\\spatie\\laravel-settings\\src\\SettingsRepositories\\DatabaseSettingsRepository.php(28): Illuminate\\Database\\Eloquent\\Builder->get(Array)
#19 D:\\Workstation\\hoomi\\vendor\\spatie\\laravel-settings\\src\\SettingsMapper.php(86): Spatie\\LaravelSettings\\SettingsRepositories\\DatabaseSettingsRepository->getPropertiesInGroup('developer')
#20 D:\\Workstation\\hoomi\\vendor\\spatie\\laravel-settings\\src\\SettingsMapper.php(37): Spatie\\LaravelSettings\\SettingsMapper->fetchProperties('App\\\\Settings\\\\De...', Object(Illuminate\\Support\\Collection))
#21 D:\\Workstation\\hoomi\\vendor\\spatie\\laravel-settings\\src\\Settings.php(277): Spatie\\LaravelSettings\\SettingsMapper->load('App\\\\Settings\\\\De...')
#22 D:\\Workstation\\hoomi\\vendor\\spatie\\laravel-settings\\src\\Settings.php(101): Spatie\\LaravelSettings\\Settings->loadValues()
#23 D:\\Workstation\\hoomi\\app\\Providers\\AppServiceProvider.php(60): Spatie\\LaravelSettings\\Settings->__get('debug_mode')
#24 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Tasawk\\Providers\\AppServiceProvider->boot()
#25 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#26 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#27 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#28 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#29 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1059): Illuminate\\Container\\Container->call(Array)
#30 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1040): Illuminate\\Foundation\\Application->bootProvider(Object(Tasawk\\Providers\\AppServiceProvider))
#31 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(Tasawk\\Providers\\AppServiceProvider), 63)
#32 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1041): array_walk(Array, Object(Closure))
#33 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#34 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(286): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#35 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(447): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#36 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(199): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#37 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#38 {main}
"} 
[2025-05-28 09:23:17] production.ERROR: SQLSTATE[HY000] [1045] Access denied for user 'forge'@'localhost' (using password: YES) (Connection: mysql, SQL: select `name`, `payload` from `settings` where `group` = developer) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 1045): SQLSTATE[HY000] [1045] Access denied for user 'forge'@'localhost' (using password: YES) (Connection: mysql, SQL: select `name`, `payload` from `settings` where `group` = developer) at D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:829)
[stacktrace]
#0 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('select `name`, ...', Array, Object(Closure))
#1 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(431): Illuminate\\Database\\Connection->run('select `name`, ...', Array, Object(Closure))
#2 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2914): Illuminate\\Database\\Connection->select('select `name`, ...', Array, true)
#3 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2902): Illuminate\\Database\\Query\\Builder->runSelect()
#4 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3456): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#5 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2903): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#6 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(739): Illuminate\\Database\\Query\\Builder->get(Array)
#7 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(723): Illuminate\\Database\\Eloquent\\Builder->getModels(Array)
#8 D:\\Workstation\\hoomi\\vendor\\spatie\\laravel-settings\\src\\SettingsRepositories\\DatabaseSettingsRepository.php(28): Illuminate\\Database\\Eloquent\\Builder->get(Array)
#9 D:\\Workstation\\hoomi\\vendor\\spatie\\laravel-settings\\src\\SettingsMapper.php(86): Spatie\\LaravelSettings\\SettingsRepositories\\DatabaseSettingsRepository->getPropertiesInGroup('developer')
#10 D:\\Workstation\\hoomi\\vendor\\spatie\\laravel-settings\\src\\SettingsMapper.php(37): Spatie\\LaravelSettings\\SettingsMapper->fetchProperties('App\\\\Settings\\\\De...', Object(Illuminate\\Support\\Collection))
#11 D:\\Workstation\\hoomi\\vendor\\spatie\\laravel-settings\\src\\Settings.php(277): Spatie\\LaravelSettings\\SettingsMapper->load('App\\\\Settings\\\\De...')
#12 D:\\Workstation\\hoomi\\vendor\\spatie\\laravel-settings\\src\\Settings.php(101): Spatie\\LaravelSettings\\Settings->loadValues()
#13 D:\\Workstation\\hoomi\\app\\Providers\\AppServiceProvider.php(60): Spatie\\LaravelSettings\\Settings->__get('debug_mode')
#14 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Tasawk\\Providers\\AppServiceProvider->boot()
#15 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#16 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#17 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#18 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#19 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1059): Illuminate\\Container\\Container->call(Array)
#20 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1040): Illuminate\\Foundation\\Application->bootProvider(Object(Tasawk\\Providers\\AppServiceProvider))
#21 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(Tasawk\\Providers\\AppServiceProvider), 63)
#22 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1041): array_walk(Array, Object(Closure))
#23 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#24 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(286): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#25 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(447): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#26 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(199): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#27 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#28 {main}

[previous exception] [object] (PDOException(code: 1045): SQLSTATE[HY000] [1045] Access denied for user 'forge'@'localhost' (using password: YES) at D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php:65)
[stacktrace]
#0 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(65): PDO->__construct('mysql:host=127....', 'forge', '', Array)
#1 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(45): Illuminate\\Database\\Connectors\\Connector->createPdoConnection('mysql:host=127....', 'forge', '', Array)
#2 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\MySqlConnector.php(24): Illuminate\\Database\\Connectors\\Connector->createConnection('mysql:host=127....', Array, Array)
#3 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(184): Illuminate\\Database\\Connectors\\MySqlConnector->connect(Array)
#4 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->Illuminate\\Database\\Connectors\\{closure}()
#5 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1339): call_user_func(Object(Closure))
#6 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1375): Illuminate\\Database\\Connection->getPdo()
#7 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(528): Illuminate\\Database\\Connection->getReadPdo()
#8 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(423): Illuminate\\Database\\Connection->getPdoForSelect(true)
#9 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(816): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select `name`, ...', Array)
#10 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('select `name`, ...', Array, Object(Closure))
#11 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(431): Illuminate\\Database\\Connection->run('select `name`, ...', Array, Object(Closure))
#12 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2914): Illuminate\\Database\\Connection->select('select `name`, ...', Array, true)
#13 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2902): Illuminate\\Database\\Query\\Builder->runSelect()
#14 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3456): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#15 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2903): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#16 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(739): Illuminate\\Database\\Query\\Builder->get(Array)
#17 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(723): Illuminate\\Database\\Eloquent\\Builder->getModels(Array)
#18 D:\\Workstation\\hoomi\\vendor\\spatie\\laravel-settings\\src\\SettingsRepositories\\DatabaseSettingsRepository.php(28): Illuminate\\Database\\Eloquent\\Builder->get(Array)
#19 D:\\Workstation\\hoomi\\vendor\\spatie\\laravel-settings\\src\\SettingsMapper.php(86): Spatie\\LaravelSettings\\SettingsRepositories\\DatabaseSettingsRepository->getPropertiesInGroup('developer')
#20 D:\\Workstation\\hoomi\\vendor\\spatie\\laravel-settings\\src\\SettingsMapper.php(37): Spatie\\LaravelSettings\\SettingsMapper->fetchProperties('App\\\\Settings\\\\De...', Object(Illuminate\\Support\\Collection))
#21 D:\\Workstation\\hoomi\\vendor\\spatie\\laravel-settings\\src\\Settings.php(277): Spatie\\LaravelSettings\\SettingsMapper->load('App\\\\Settings\\\\De...')
#22 D:\\Workstation\\hoomi\\vendor\\spatie\\laravel-settings\\src\\Settings.php(101): Spatie\\LaravelSettings\\Settings->loadValues()
#23 D:\\Workstation\\hoomi\\app\\Providers\\AppServiceProvider.php(60): Spatie\\LaravelSettings\\Settings->__get('debug_mode')
#24 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Tasawk\\Providers\\AppServiceProvider->boot()
#25 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#26 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#27 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#28 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#29 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1059): Illuminate\\Container\\Container->call(Array)
#30 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1040): Illuminate\\Foundation\\Application->bootProvider(Object(Tasawk\\Providers\\AppServiceProvider))
#31 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(Tasawk\\Providers\\AppServiceProvider), 63)
#32 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1041): array_walk(Array, Object(Closure))
#33 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#34 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(286): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#35 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(447): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#36 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(199): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#37 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#38 {main}
"} 
[2025-05-28 09:23:17] production.ERROR: SQLSTATE[HY000] [1045] Access denied for user 'forge'@'localhost' (using password: YES) (Connection: mysql, SQL: select `name`, `payload` from `settings` where `group` = developer) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 1045): SQLSTATE[HY000] [1045] Access denied for user 'forge'@'localhost' (using password: YES) (Connection: mysql, SQL: select `name`, `payload` from `settings` where `group` = developer) at D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:829)
[stacktrace]
#0 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('select `name`, ...', Array, Object(Closure))
#1 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(431): Illuminate\\Database\\Connection->run('select `name`, ...', Array, Object(Closure))
#2 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2914): Illuminate\\Database\\Connection->select('select `name`, ...', Array, true)
#3 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2902): Illuminate\\Database\\Query\\Builder->runSelect()
#4 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3456): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#5 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2903): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#6 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(739): Illuminate\\Database\\Query\\Builder->get(Array)
#7 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(723): Illuminate\\Database\\Eloquent\\Builder->getModels(Array)
#8 D:\\Workstation\\hoomi\\vendor\\spatie\\laravel-settings\\src\\SettingsRepositories\\DatabaseSettingsRepository.php(28): Illuminate\\Database\\Eloquent\\Builder->get(Array)
#9 D:\\Workstation\\hoomi\\vendor\\spatie\\laravel-settings\\src\\SettingsMapper.php(86): Spatie\\LaravelSettings\\SettingsRepositories\\DatabaseSettingsRepository->getPropertiesInGroup('developer')
#10 D:\\Workstation\\hoomi\\vendor\\spatie\\laravel-settings\\src\\SettingsMapper.php(37): Spatie\\LaravelSettings\\SettingsMapper->fetchProperties('App\\\\Settings\\\\De...', Object(Illuminate\\Support\\Collection))
#11 D:\\Workstation\\hoomi\\vendor\\spatie\\laravel-settings\\src\\Settings.php(277): Spatie\\LaravelSettings\\SettingsMapper->load('App\\\\Settings\\\\De...')
#12 D:\\Workstation\\hoomi\\vendor\\spatie\\laravel-settings\\src\\Settings.php(101): Spatie\\LaravelSettings\\Settings->loadValues()
#13 D:\\Workstation\\hoomi\\app\\Providers\\AppServiceProvider.php(60): Spatie\\LaravelSettings\\Settings->__get('debug_mode')
#14 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Tasawk\\Providers\\AppServiceProvider->boot()
#15 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#16 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#17 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#18 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#19 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1059): Illuminate\\Container\\Container->call(Array)
#20 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1040): Illuminate\\Foundation\\Application->bootProvider(Object(Tasawk\\Providers\\AppServiceProvider))
#21 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(Tasawk\\Providers\\AppServiceProvider), 63)
#22 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1041): array_walk(Array, Object(Closure))
#23 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#24 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(286): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#25 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(447): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#26 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(199): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#27 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#28 {main}

[previous exception] [object] (PDOException(code: 1045): SQLSTATE[HY000] [1045] Access denied for user 'forge'@'localhost' (using password: YES) at D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php:65)
[stacktrace]
#0 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(65): PDO->__construct('mysql:host=127....', 'forge', '', Array)
#1 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(45): Illuminate\\Database\\Connectors\\Connector->createPdoConnection('mysql:host=127....', 'forge', '', Array)
#2 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\MySqlConnector.php(24): Illuminate\\Database\\Connectors\\Connector->createConnection('mysql:host=127....', Array, Array)
#3 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(184): Illuminate\\Database\\Connectors\\MySqlConnector->connect(Array)
#4 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->Illuminate\\Database\\Connectors\\{closure}()
#5 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1339): call_user_func(Object(Closure))
#6 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1375): Illuminate\\Database\\Connection->getPdo()
#7 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(528): Illuminate\\Database\\Connection->getReadPdo()
#8 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(423): Illuminate\\Database\\Connection->getPdoForSelect(true)
#9 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(816): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select `name`, ...', Array)
#10 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('select `name`, ...', Array, Object(Closure))
#11 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(431): Illuminate\\Database\\Connection->run('select `name`, ...', Array, Object(Closure))
#12 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2914): Illuminate\\Database\\Connection->select('select `name`, ...', Array, true)
#13 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2902): Illuminate\\Database\\Query\\Builder->runSelect()
#14 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3456): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#15 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2903): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#16 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(739): Illuminate\\Database\\Query\\Builder->get(Array)
#17 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(723): Illuminate\\Database\\Eloquent\\Builder->getModels(Array)
#18 D:\\Workstation\\hoomi\\vendor\\spatie\\laravel-settings\\src\\SettingsRepositories\\DatabaseSettingsRepository.php(28): Illuminate\\Database\\Eloquent\\Builder->get(Array)
#19 D:\\Workstation\\hoomi\\vendor\\spatie\\laravel-settings\\src\\SettingsMapper.php(86): Spatie\\LaravelSettings\\SettingsRepositories\\DatabaseSettingsRepository->getPropertiesInGroup('developer')
#20 D:\\Workstation\\hoomi\\vendor\\spatie\\laravel-settings\\src\\SettingsMapper.php(37): Spatie\\LaravelSettings\\SettingsMapper->fetchProperties('App\\\\Settings\\\\De...', Object(Illuminate\\Support\\Collection))
#21 D:\\Workstation\\hoomi\\vendor\\spatie\\laravel-settings\\src\\Settings.php(277): Spatie\\LaravelSettings\\SettingsMapper->load('App\\\\Settings\\\\De...')
#22 D:\\Workstation\\hoomi\\vendor\\spatie\\laravel-settings\\src\\Settings.php(101): Spatie\\LaravelSettings\\Settings->loadValues()
#23 D:\\Workstation\\hoomi\\app\\Providers\\AppServiceProvider.php(60): Spatie\\LaravelSettings\\Settings->__get('debug_mode')
#24 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Tasawk\\Providers\\AppServiceProvider->boot()
#25 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#26 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#27 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#28 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#29 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1059): Illuminate\\Container\\Container->call(Array)
#30 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1040): Illuminate\\Foundation\\Application->bootProvider(Object(Tasawk\\Providers\\AppServiceProvider))
#31 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(Tasawk\\Providers\\AppServiceProvider), 63)
#32 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1041): array_walk(Array, Object(Closure))
#33 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#34 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(286): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#35 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(447): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#36 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(199): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#37 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#38 {main}
"} 
[2025-05-28 09:24:13] production.ERROR: SQLSTATE[HY000] [1045] Access denied for user 'forge'@'localhost' (using password: YES) (Connection: mysql, SQL: select `name`, `payload` from `settings` where `group` = developer) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 1045): SQLSTATE[HY000] [1045] Access denied for user 'forge'@'localhost' (using password: YES) (Connection: mysql, SQL: select `name`, `payload` from `settings` where `group` = developer) at D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:829)
[stacktrace]
#0 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('select `name`, ...', Array, Object(Closure))
#1 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(431): Illuminate\\Database\\Connection->run('select `name`, ...', Array, Object(Closure))
#2 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2914): Illuminate\\Database\\Connection->select('select `name`, ...', Array, true)
#3 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2902): Illuminate\\Database\\Query\\Builder->runSelect()
#4 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3456): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#5 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2903): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#6 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(739): Illuminate\\Database\\Query\\Builder->get(Array)
#7 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(723): Illuminate\\Database\\Eloquent\\Builder->getModels(Array)
#8 D:\\Workstation\\hoomi\\vendor\\spatie\\laravel-settings\\src\\SettingsRepositories\\DatabaseSettingsRepository.php(28): Illuminate\\Database\\Eloquent\\Builder->get(Array)
#9 D:\\Workstation\\hoomi\\vendor\\spatie\\laravel-settings\\src\\SettingsMapper.php(86): Spatie\\LaravelSettings\\SettingsRepositories\\DatabaseSettingsRepository->getPropertiesInGroup('developer')
#10 D:\\Workstation\\hoomi\\vendor\\spatie\\laravel-settings\\src\\SettingsMapper.php(37): Spatie\\LaravelSettings\\SettingsMapper->fetchProperties('App\\\\Settings\\\\De...', Object(Illuminate\\Support\\Collection))
#11 D:\\Workstation\\hoomi\\vendor\\spatie\\laravel-settings\\src\\Settings.php(277): Spatie\\LaravelSettings\\SettingsMapper->load('App\\\\Settings\\\\De...')
#12 D:\\Workstation\\hoomi\\vendor\\spatie\\laravel-settings\\src\\Settings.php(101): Spatie\\LaravelSettings\\Settings->loadValues()
#13 D:\\Workstation\\hoomi\\app\\Providers\\AppServiceProvider.php(60): Spatie\\LaravelSettings\\Settings->__get('debug_mode')
#14 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Tasawk\\Providers\\AppServiceProvider->boot()
#15 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#16 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#17 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#18 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#19 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1059): Illuminate\\Container\\Container->call(Array)
#20 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1040): Illuminate\\Foundation\\Application->bootProvider(Object(Tasawk\\Providers\\AppServiceProvider))
#21 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(Tasawk\\Providers\\AppServiceProvider), 63)
#22 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1041): array_walk(Array, Object(Closure))
#23 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#24 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(286): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#25 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(447): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#26 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(199): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#27 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#28 {main}

[previous exception] [object] (PDOException(code: 1045): SQLSTATE[HY000] [1045] Access denied for user 'forge'@'localhost' (using password: YES) at D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php:65)
[stacktrace]
#0 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(65): PDO->__construct('mysql:host=127....', 'forge', '', Array)
#1 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(45): Illuminate\\Database\\Connectors\\Connector->createPdoConnection('mysql:host=127....', 'forge', '', Array)
#2 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\MySqlConnector.php(24): Illuminate\\Database\\Connectors\\Connector->createConnection('mysql:host=127....', Array, Array)
#3 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(184): Illuminate\\Database\\Connectors\\MySqlConnector->connect(Array)
#4 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->Illuminate\\Database\\Connectors\\{closure}()
#5 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1339): call_user_func(Object(Closure))
#6 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1375): Illuminate\\Database\\Connection->getPdo()
#7 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(528): Illuminate\\Database\\Connection->getReadPdo()
#8 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(423): Illuminate\\Database\\Connection->getPdoForSelect(true)
#9 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(816): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select `name`, ...', Array)
#10 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('select `name`, ...', Array, Object(Closure))
#11 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(431): Illuminate\\Database\\Connection->run('select `name`, ...', Array, Object(Closure))
#12 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2914): Illuminate\\Database\\Connection->select('select `name`, ...', Array, true)
#13 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2902): Illuminate\\Database\\Query\\Builder->runSelect()
#14 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3456): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#15 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2903): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#16 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(739): Illuminate\\Database\\Query\\Builder->get(Array)
#17 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(723): Illuminate\\Database\\Eloquent\\Builder->getModels(Array)
#18 D:\\Workstation\\hoomi\\vendor\\spatie\\laravel-settings\\src\\SettingsRepositories\\DatabaseSettingsRepository.php(28): Illuminate\\Database\\Eloquent\\Builder->get(Array)
#19 D:\\Workstation\\hoomi\\vendor\\spatie\\laravel-settings\\src\\SettingsMapper.php(86): Spatie\\LaravelSettings\\SettingsRepositories\\DatabaseSettingsRepository->getPropertiesInGroup('developer')
#20 D:\\Workstation\\hoomi\\vendor\\spatie\\laravel-settings\\src\\SettingsMapper.php(37): Spatie\\LaravelSettings\\SettingsMapper->fetchProperties('App\\\\Settings\\\\De...', Object(Illuminate\\Support\\Collection))
#21 D:\\Workstation\\hoomi\\vendor\\spatie\\laravel-settings\\src\\Settings.php(277): Spatie\\LaravelSettings\\SettingsMapper->load('App\\\\Settings\\\\De...')
#22 D:\\Workstation\\hoomi\\vendor\\spatie\\laravel-settings\\src\\Settings.php(101): Spatie\\LaravelSettings\\Settings->loadValues()
#23 D:\\Workstation\\hoomi\\app\\Providers\\AppServiceProvider.php(60): Spatie\\LaravelSettings\\Settings->__get('debug_mode')
#24 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Tasawk\\Providers\\AppServiceProvider->boot()
#25 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#26 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#27 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#28 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#29 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1059): Illuminate\\Container\\Container->call(Array)
#30 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1040): Illuminate\\Foundation\\Application->bootProvider(Object(Tasawk\\Providers\\AppServiceProvider))
#31 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(Tasawk\\Providers\\AppServiceProvider), 63)
#32 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1041): array_walk(Array, Object(Closure))
#33 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#34 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(286): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#35 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(447): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#36 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(199): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#37 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#38 {main}
"} 
[2025-05-28 09:24:13] production.ERROR: SQLSTATE[HY000] [1045] Access denied for user 'forge'@'localhost' (using password: YES) (Connection: mysql, SQL: select `name`, `payload` from `settings` where `group` = developer) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 1045): SQLSTATE[HY000] [1045] Access denied for user 'forge'@'localhost' (using password: YES) (Connection: mysql, SQL: select `name`, `payload` from `settings` where `group` = developer) at D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:829)
[stacktrace]
#0 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('select `name`, ...', Array, Object(Closure))
#1 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(431): Illuminate\\Database\\Connection->run('select `name`, ...', Array, Object(Closure))
#2 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2914): Illuminate\\Database\\Connection->select('select `name`, ...', Array, true)
#3 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2902): Illuminate\\Database\\Query\\Builder->runSelect()
#4 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3456): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#5 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2903): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#6 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(739): Illuminate\\Database\\Query\\Builder->get(Array)
#7 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(723): Illuminate\\Database\\Eloquent\\Builder->getModels(Array)
#8 D:\\Workstation\\hoomi\\vendor\\spatie\\laravel-settings\\src\\SettingsRepositories\\DatabaseSettingsRepository.php(28): Illuminate\\Database\\Eloquent\\Builder->get(Array)
#9 D:\\Workstation\\hoomi\\vendor\\spatie\\laravel-settings\\src\\SettingsMapper.php(86): Spatie\\LaravelSettings\\SettingsRepositories\\DatabaseSettingsRepository->getPropertiesInGroup('developer')
#10 D:\\Workstation\\hoomi\\vendor\\spatie\\laravel-settings\\src\\SettingsMapper.php(37): Spatie\\LaravelSettings\\SettingsMapper->fetchProperties('App\\\\Settings\\\\De...', Object(Illuminate\\Support\\Collection))
#11 D:\\Workstation\\hoomi\\vendor\\spatie\\laravel-settings\\src\\Settings.php(277): Spatie\\LaravelSettings\\SettingsMapper->load('App\\\\Settings\\\\De...')
#12 D:\\Workstation\\hoomi\\vendor\\spatie\\laravel-settings\\src\\Settings.php(101): Spatie\\LaravelSettings\\Settings->loadValues()
#13 D:\\Workstation\\hoomi\\app\\Providers\\AppServiceProvider.php(60): Spatie\\LaravelSettings\\Settings->__get('debug_mode')
#14 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Tasawk\\Providers\\AppServiceProvider->boot()
#15 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#16 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#17 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#18 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#19 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1059): Illuminate\\Container\\Container->call(Array)
#20 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1040): Illuminate\\Foundation\\Application->bootProvider(Object(Tasawk\\Providers\\AppServiceProvider))
#21 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(Tasawk\\Providers\\AppServiceProvider), 63)
#22 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1041): array_walk(Array, Object(Closure))
#23 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#24 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(286): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#25 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(447): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#26 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(199): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#27 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#28 {main}

[previous exception] [object] (PDOException(code: 1045): SQLSTATE[HY000] [1045] Access denied for user 'forge'@'localhost' (using password: YES) at D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php:65)
[stacktrace]
#0 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(65): PDO->__construct('mysql:host=127....', 'forge', '', Array)
#1 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(45): Illuminate\\Database\\Connectors\\Connector->createPdoConnection('mysql:host=127....', 'forge', '', Array)
#2 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\MySqlConnector.php(24): Illuminate\\Database\\Connectors\\Connector->createConnection('mysql:host=127....', Array, Array)
#3 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(184): Illuminate\\Database\\Connectors\\MySqlConnector->connect(Array)
#4 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->Illuminate\\Database\\Connectors\\{closure}()
#5 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1339): call_user_func(Object(Closure))
#6 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1375): Illuminate\\Database\\Connection->getPdo()
#7 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(528): Illuminate\\Database\\Connection->getReadPdo()
#8 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(423): Illuminate\\Database\\Connection->getPdoForSelect(true)
#9 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(816): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select `name`, ...', Array)
#10 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('select `name`, ...', Array, Object(Closure))
#11 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(431): Illuminate\\Database\\Connection->run('select `name`, ...', Array, Object(Closure))
#12 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2914): Illuminate\\Database\\Connection->select('select `name`, ...', Array, true)
#13 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2902): Illuminate\\Database\\Query\\Builder->runSelect()
#14 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3456): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#15 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2903): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#16 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(739): Illuminate\\Database\\Query\\Builder->get(Array)
#17 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(723): Illuminate\\Database\\Eloquent\\Builder->getModels(Array)
#18 D:\\Workstation\\hoomi\\vendor\\spatie\\laravel-settings\\src\\SettingsRepositories\\DatabaseSettingsRepository.php(28): Illuminate\\Database\\Eloquent\\Builder->get(Array)
#19 D:\\Workstation\\hoomi\\vendor\\spatie\\laravel-settings\\src\\SettingsMapper.php(86): Spatie\\LaravelSettings\\SettingsRepositories\\DatabaseSettingsRepository->getPropertiesInGroup('developer')
#20 D:\\Workstation\\hoomi\\vendor\\spatie\\laravel-settings\\src\\SettingsMapper.php(37): Spatie\\LaravelSettings\\SettingsMapper->fetchProperties('App\\\\Settings\\\\De...', Object(Illuminate\\Support\\Collection))
#21 D:\\Workstation\\hoomi\\vendor\\spatie\\laravel-settings\\src\\Settings.php(277): Spatie\\LaravelSettings\\SettingsMapper->load('App\\\\Settings\\\\De...')
#22 D:\\Workstation\\hoomi\\vendor\\spatie\\laravel-settings\\src\\Settings.php(101): Spatie\\LaravelSettings\\Settings->loadValues()
#23 D:\\Workstation\\hoomi\\app\\Providers\\AppServiceProvider.php(60): Spatie\\LaravelSettings\\Settings->__get('debug_mode')
#24 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Tasawk\\Providers\\AppServiceProvider->boot()
#25 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#26 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#27 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#28 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#29 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1059): Illuminate\\Container\\Container->call(Array)
#30 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1040): Illuminate\\Foundation\\Application->bootProvider(Object(Tasawk\\Providers\\AppServiceProvider))
#31 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(Tasawk\\Providers\\AppServiceProvider), 63)
#32 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1041): array_walk(Array, Object(Closure))
#33 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#34 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(286): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#35 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(447): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#36 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(199): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#37 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#38 {main}
"} 
[2025-05-28 09:26:29] production.ERROR: SQLSTATE[HY000] [1045] Access denied for user 'forge'@'localhost' (using password: YES) (Connection: mysql, SQL: select `name`, `payload` from `settings` where `group` = developer) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 1045): SQLSTATE[HY000] [1045] Access denied for user 'forge'@'localhost' (using password: YES) (Connection: mysql, SQL: select `name`, `payload` from `settings` where `group` = developer) at D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:829)
[stacktrace]
#0 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('select `name`, ...', Array, Object(Closure))
#1 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(431): Illuminate\\Database\\Connection->run('select `name`, ...', Array, Object(Closure))
#2 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2914): Illuminate\\Database\\Connection->select('select `name`, ...', Array, true)
#3 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2902): Illuminate\\Database\\Query\\Builder->runSelect()
#4 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3456): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#5 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2903): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#6 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(739): Illuminate\\Database\\Query\\Builder->get(Array)
#7 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(723): Illuminate\\Database\\Eloquent\\Builder->getModels(Array)
#8 D:\\Workstation\\hoomi\\vendor\\spatie\\laravel-settings\\src\\SettingsRepositories\\DatabaseSettingsRepository.php(28): Illuminate\\Database\\Eloquent\\Builder->get(Array)
#9 D:\\Workstation\\hoomi\\vendor\\spatie\\laravel-settings\\src\\SettingsMapper.php(86): Spatie\\LaravelSettings\\SettingsRepositories\\DatabaseSettingsRepository->getPropertiesInGroup('developer')
#10 D:\\Workstation\\hoomi\\vendor\\spatie\\laravel-settings\\src\\SettingsMapper.php(37): Spatie\\LaravelSettings\\SettingsMapper->fetchProperties('App\\\\Settings\\\\De...', Object(Illuminate\\Support\\Collection))
#11 D:\\Workstation\\hoomi\\vendor\\spatie\\laravel-settings\\src\\Settings.php(277): Spatie\\LaravelSettings\\SettingsMapper->load('App\\\\Settings\\\\De...')
#12 D:\\Workstation\\hoomi\\vendor\\spatie\\laravel-settings\\src\\Settings.php(101): Spatie\\LaravelSettings\\Settings->loadValues()
#13 D:\\Workstation\\hoomi\\app\\Providers\\AppServiceProvider.php(60): Spatie\\LaravelSettings\\Settings->__get('debug_mode')
#14 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Tasawk\\Providers\\AppServiceProvider->boot()
#15 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#16 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#17 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#18 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#19 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1059): Illuminate\\Container\\Container->call(Array)
#20 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1040): Illuminate\\Foundation\\Application->bootProvider(Object(Tasawk\\Providers\\AppServiceProvider))
#21 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(Tasawk\\Providers\\AppServiceProvider), 63)
#22 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1041): array_walk(Array, Object(Closure))
#23 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#24 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(286): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#25 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(447): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#26 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(199): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#27 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#28 {main}

[previous exception] [object] (PDOException(code: 1045): SQLSTATE[HY000] [1045] Access denied for user 'forge'@'localhost' (using password: YES) at D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php:65)
[stacktrace]
#0 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(65): PDO->__construct('mysql:host=127....', 'forge', '', Array)
#1 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(45): Illuminate\\Database\\Connectors\\Connector->createPdoConnection('mysql:host=127....', 'forge', '', Array)
#2 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\MySqlConnector.php(24): Illuminate\\Database\\Connectors\\Connector->createConnection('mysql:host=127....', Array, Array)
#3 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(184): Illuminate\\Database\\Connectors\\MySqlConnector->connect(Array)
#4 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->Illuminate\\Database\\Connectors\\{closure}()
#5 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1339): call_user_func(Object(Closure))
#6 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1375): Illuminate\\Database\\Connection->getPdo()
#7 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(528): Illuminate\\Database\\Connection->getReadPdo()
#8 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(423): Illuminate\\Database\\Connection->getPdoForSelect(true)
#9 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(816): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select `name`, ...', Array)
#10 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('select `name`, ...', Array, Object(Closure))
#11 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(431): Illuminate\\Database\\Connection->run('select `name`, ...', Array, Object(Closure))
#12 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2914): Illuminate\\Database\\Connection->select('select `name`, ...', Array, true)
#13 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2902): Illuminate\\Database\\Query\\Builder->runSelect()
#14 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3456): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#15 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2903): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#16 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(739): Illuminate\\Database\\Query\\Builder->get(Array)
#17 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(723): Illuminate\\Database\\Eloquent\\Builder->getModels(Array)
#18 D:\\Workstation\\hoomi\\vendor\\spatie\\laravel-settings\\src\\SettingsRepositories\\DatabaseSettingsRepository.php(28): Illuminate\\Database\\Eloquent\\Builder->get(Array)
#19 D:\\Workstation\\hoomi\\vendor\\spatie\\laravel-settings\\src\\SettingsMapper.php(86): Spatie\\LaravelSettings\\SettingsRepositories\\DatabaseSettingsRepository->getPropertiesInGroup('developer')
#20 D:\\Workstation\\hoomi\\vendor\\spatie\\laravel-settings\\src\\SettingsMapper.php(37): Spatie\\LaravelSettings\\SettingsMapper->fetchProperties('App\\\\Settings\\\\De...', Object(Illuminate\\Support\\Collection))
#21 D:\\Workstation\\hoomi\\vendor\\spatie\\laravel-settings\\src\\Settings.php(277): Spatie\\LaravelSettings\\SettingsMapper->load('App\\\\Settings\\\\De...')
#22 D:\\Workstation\\hoomi\\vendor\\spatie\\laravel-settings\\src\\Settings.php(101): Spatie\\LaravelSettings\\Settings->loadValues()
#23 D:\\Workstation\\hoomi\\app\\Providers\\AppServiceProvider.php(60): Spatie\\LaravelSettings\\Settings->__get('debug_mode')
#24 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Tasawk\\Providers\\AppServiceProvider->boot()
#25 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#26 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#27 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#28 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#29 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1059): Illuminate\\Container\\Container->call(Array)
#30 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1040): Illuminate\\Foundation\\Application->bootProvider(Object(Tasawk\\Providers\\AppServiceProvider))
#31 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(Tasawk\\Providers\\AppServiceProvider), 63)
#32 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1041): array_walk(Array, Object(Closure))
#33 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#34 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(286): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#35 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(447): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#36 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(199): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#37 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#38 {main}
"} 
[2025-05-28 09:27:11] production.ERROR: SQLSTATE[HY000] [1045] Access denied for user 'forge'@'localhost' (using password: YES) (Connection: mysql, SQL: select `name`, `payload` from `settings` where `group` = developer) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 1045): SQLSTATE[HY000] [1045] Access denied for user 'forge'@'localhost' (using password: YES) (Connection: mysql, SQL: select `name`, `payload` from `settings` where `group` = developer) at D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:829)
[stacktrace]
#0 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('select `name`, ...', Array, Object(Closure))
#1 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(431): Illuminate\\Database\\Connection->run('select `name`, ...', Array, Object(Closure))
#2 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2914): Illuminate\\Database\\Connection->select('select `name`, ...', Array, true)
#3 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2902): Illuminate\\Database\\Query\\Builder->runSelect()
#4 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3456): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#5 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2903): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#6 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(739): Illuminate\\Database\\Query\\Builder->get(Array)
#7 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(723): Illuminate\\Database\\Eloquent\\Builder->getModels(Array)
#8 D:\\Workstation\\hoomi\\vendor\\spatie\\laravel-settings\\src\\SettingsRepositories\\DatabaseSettingsRepository.php(28): Illuminate\\Database\\Eloquent\\Builder->get(Array)
#9 D:\\Workstation\\hoomi\\vendor\\spatie\\laravel-settings\\src\\SettingsMapper.php(86): Spatie\\LaravelSettings\\SettingsRepositories\\DatabaseSettingsRepository->getPropertiesInGroup('developer')
#10 D:\\Workstation\\hoomi\\vendor\\spatie\\laravel-settings\\src\\SettingsMapper.php(37): Spatie\\LaravelSettings\\SettingsMapper->fetchProperties('App\\\\Settings\\\\De...', Object(Illuminate\\Support\\Collection))
#11 D:\\Workstation\\hoomi\\vendor\\spatie\\laravel-settings\\src\\Settings.php(277): Spatie\\LaravelSettings\\SettingsMapper->load('App\\\\Settings\\\\De...')
#12 D:\\Workstation\\hoomi\\vendor\\spatie\\laravel-settings\\src\\Settings.php(101): Spatie\\LaravelSettings\\Settings->loadValues()
#13 D:\\Workstation\\hoomi\\app\\Providers\\AppServiceProvider.php(60): Spatie\\LaravelSettings\\Settings->__get('debug_mode')
#14 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Tasawk\\Providers\\AppServiceProvider->boot()
#15 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#16 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#17 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#18 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#19 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1059): Illuminate\\Container\\Container->call(Array)
#20 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1040): Illuminate\\Foundation\\Application->bootProvider(Object(Tasawk\\Providers\\AppServiceProvider))
#21 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(Tasawk\\Providers\\AppServiceProvider), 63)
#22 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1041): array_walk(Array, Object(Closure))
#23 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#24 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(286): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#25 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(447): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#26 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(199): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#27 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#28 {main}

[previous exception] [object] (PDOException(code: 1045): SQLSTATE[HY000] [1045] Access denied for user 'forge'@'localhost' (using password: YES) at D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php:65)
[stacktrace]
#0 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(65): PDO->__construct('mysql:host=127....', 'forge', '', Array)
#1 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(45): Illuminate\\Database\\Connectors\\Connector->createPdoConnection('mysql:host=127....', 'forge', '', Array)
#2 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\MySqlConnector.php(24): Illuminate\\Database\\Connectors\\Connector->createConnection('mysql:host=127....', Array, Array)
#3 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(184): Illuminate\\Database\\Connectors\\MySqlConnector->connect(Array)
#4 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->Illuminate\\Database\\Connectors\\{closure}()
#5 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1339): call_user_func(Object(Closure))
#6 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1375): Illuminate\\Database\\Connection->getPdo()
#7 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(528): Illuminate\\Database\\Connection->getReadPdo()
#8 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(423): Illuminate\\Database\\Connection->getPdoForSelect(true)
#9 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(816): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select `name`, ...', Array)
#10 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('select `name`, ...', Array, Object(Closure))
#11 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(431): Illuminate\\Database\\Connection->run('select `name`, ...', Array, Object(Closure))
#12 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2914): Illuminate\\Database\\Connection->select('select `name`, ...', Array, true)
#13 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2902): Illuminate\\Database\\Query\\Builder->runSelect()
#14 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3456): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#15 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2903): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#16 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(739): Illuminate\\Database\\Query\\Builder->get(Array)
#17 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(723): Illuminate\\Database\\Eloquent\\Builder->getModels(Array)
#18 D:\\Workstation\\hoomi\\vendor\\spatie\\laravel-settings\\src\\SettingsRepositories\\DatabaseSettingsRepository.php(28): Illuminate\\Database\\Eloquent\\Builder->get(Array)
#19 D:\\Workstation\\hoomi\\vendor\\spatie\\laravel-settings\\src\\SettingsMapper.php(86): Spatie\\LaravelSettings\\SettingsRepositories\\DatabaseSettingsRepository->getPropertiesInGroup('developer')
#20 D:\\Workstation\\hoomi\\vendor\\spatie\\laravel-settings\\src\\SettingsMapper.php(37): Spatie\\LaravelSettings\\SettingsMapper->fetchProperties('App\\\\Settings\\\\De...', Object(Illuminate\\Support\\Collection))
#21 D:\\Workstation\\hoomi\\vendor\\spatie\\laravel-settings\\src\\Settings.php(277): Spatie\\LaravelSettings\\SettingsMapper->load('App\\\\Settings\\\\De...')
#22 D:\\Workstation\\hoomi\\vendor\\spatie\\laravel-settings\\src\\Settings.php(101): Spatie\\LaravelSettings\\Settings->loadValues()
#23 D:\\Workstation\\hoomi\\app\\Providers\\AppServiceProvider.php(60): Spatie\\LaravelSettings\\Settings->__get('debug_mode')
#24 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Tasawk\\Providers\\AppServiceProvider->boot()
#25 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#26 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#27 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#28 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#29 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1059): Illuminate\\Container\\Container->call(Array)
#30 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1040): Illuminate\\Foundation\\Application->bootProvider(Object(Tasawk\\Providers\\AppServiceProvider))
#31 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(Tasawk\\Providers\\AppServiceProvider), 63)
#32 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1041): array_walk(Array, Object(Closure))
#33 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#34 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(286): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#35 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(447): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#36 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(199): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#37 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#38 {main}
"} 
[2025-05-28 09:27:11] production.ERROR: SQLSTATE[HY000] [1045] Access denied for user 'forge'@'localhost' (using password: YES) (Connection: mysql, SQL: select `name`, `payload` from `settings` where `group` = developer) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 1045): SQLSTATE[HY000] [1045] Access denied for user 'forge'@'localhost' (using password: YES) (Connection: mysql, SQL: select `name`, `payload` from `settings` where `group` = developer) at D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:829)
[stacktrace]
#0 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('select `name`, ...', Array, Object(Closure))
#1 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(431): Illuminate\\Database\\Connection->run('select `name`, ...', Array, Object(Closure))
#2 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2914): Illuminate\\Database\\Connection->select('select `name`, ...', Array, true)
#3 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2902): Illuminate\\Database\\Query\\Builder->runSelect()
#4 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3456): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#5 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2903): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#6 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(739): Illuminate\\Database\\Query\\Builder->get(Array)
#7 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(723): Illuminate\\Database\\Eloquent\\Builder->getModels(Array)
#8 D:\\Workstation\\hoomi\\vendor\\spatie\\laravel-settings\\src\\SettingsRepositories\\DatabaseSettingsRepository.php(28): Illuminate\\Database\\Eloquent\\Builder->get(Array)
#9 D:\\Workstation\\hoomi\\vendor\\spatie\\laravel-settings\\src\\SettingsMapper.php(86): Spatie\\LaravelSettings\\SettingsRepositories\\DatabaseSettingsRepository->getPropertiesInGroup('developer')
#10 D:\\Workstation\\hoomi\\vendor\\spatie\\laravel-settings\\src\\SettingsMapper.php(37): Spatie\\LaravelSettings\\SettingsMapper->fetchProperties('App\\\\Settings\\\\De...', Object(Illuminate\\Support\\Collection))
#11 D:\\Workstation\\hoomi\\vendor\\spatie\\laravel-settings\\src\\Settings.php(277): Spatie\\LaravelSettings\\SettingsMapper->load('App\\\\Settings\\\\De...')
#12 D:\\Workstation\\hoomi\\vendor\\spatie\\laravel-settings\\src\\Settings.php(101): Spatie\\LaravelSettings\\Settings->loadValues()
#13 D:\\Workstation\\hoomi\\app\\Providers\\AppServiceProvider.php(60): Spatie\\LaravelSettings\\Settings->__get('debug_mode')
#14 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Tasawk\\Providers\\AppServiceProvider->boot()
#15 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#16 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#17 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#18 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#19 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1059): Illuminate\\Container\\Container->call(Array)
#20 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1040): Illuminate\\Foundation\\Application->bootProvider(Object(Tasawk\\Providers\\AppServiceProvider))
#21 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(Tasawk\\Providers\\AppServiceProvider), 63)
#22 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1041): array_walk(Array, Object(Closure))
#23 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#24 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(286): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#25 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(447): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#26 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(199): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#27 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#28 {main}

[previous exception] [object] (PDOException(code: 1045): SQLSTATE[HY000] [1045] Access denied for user 'forge'@'localhost' (using password: YES) at D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php:65)
[stacktrace]
#0 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(65): PDO->__construct('mysql:host=127....', 'forge', '', Array)
#1 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(45): Illuminate\\Database\\Connectors\\Connector->createPdoConnection('mysql:host=127....', 'forge', '', Array)
#2 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\MySqlConnector.php(24): Illuminate\\Database\\Connectors\\Connector->createConnection('mysql:host=127....', Array, Array)
#3 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(184): Illuminate\\Database\\Connectors\\MySqlConnector->connect(Array)
#4 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->Illuminate\\Database\\Connectors\\{closure}()
#5 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1339): call_user_func(Object(Closure))
#6 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1375): Illuminate\\Database\\Connection->getPdo()
#7 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(528): Illuminate\\Database\\Connection->getReadPdo()
#8 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(423): Illuminate\\Database\\Connection->getPdoForSelect(true)
#9 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(816): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select `name`, ...', Array)
#10 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('select `name`, ...', Array, Object(Closure))
#11 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(431): Illuminate\\Database\\Connection->run('select `name`, ...', Array, Object(Closure))
#12 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2914): Illuminate\\Database\\Connection->select('select `name`, ...', Array, true)
#13 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2902): Illuminate\\Database\\Query\\Builder->runSelect()
#14 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3456): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#15 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2903): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#16 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(739): Illuminate\\Database\\Query\\Builder->get(Array)
#17 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(723): Illuminate\\Database\\Eloquent\\Builder->getModels(Array)
#18 D:\\Workstation\\hoomi\\vendor\\spatie\\laravel-settings\\src\\SettingsRepositories\\DatabaseSettingsRepository.php(28): Illuminate\\Database\\Eloquent\\Builder->get(Array)
#19 D:\\Workstation\\hoomi\\vendor\\spatie\\laravel-settings\\src\\SettingsMapper.php(86): Spatie\\LaravelSettings\\SettingsRepositories\\DatabaseSettingsRepository->getPropertiesInGroup('developer')
#20 D:\\Workstation\\hoomi\\vendor\\spatie\\laravel-settings\\src\\SettingsMapper.php(37): Spatie\\LaravelSettings\\SettingsMapper->fetchProperties('App\\\\Settings\\\\De...', Object(Illuminate\\Support\\Collection))
#21 D:\\Workstation\\hoomi\\vendor\\spatie\\laravel-settings\\src\\Settings.php(277): Spatie\\LaravelSettings\\SettingsMapper->load('App\\\\Settings\\\\De...')
#22 D:\\Workstation\\hoomi\\vendor\\spatie\\laravel-settings\\src\\Settings.php(101): Spatie\\LaravelSettings\\Settings->loadValues()
#23 D:\\Workstation\\hoomi\\app\\Providers\\AppServiceProvider.php(60): Spatie\\LaravelSettings\\Settings->__get('debug_mode')
#24 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Tasawk\\Providers\\AppServiceProvider->boot()
#25 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#26 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#27 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#28 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#29 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1059): Illuminate\\Container\\Container->call(Array)
#30 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1040): Illuminate\\Foundation\\Application->bootProvider(Object(Tasawk\\Providers\\AppServiceProvider))
#31 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(Tasawk\\Providers\\AppServiceProvider), 63)
#32 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1041): array_walk(Array, Object(Closure))
#33 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#34 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(286): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#35 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(447): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#36 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(199): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#37 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#38 {main}
"} 
[2025-05-28 09:28:12] production.ERROR: SQLSTATE[HY000] [1045] Access denied for user 'forge'@'localhost' (using password: YES) (Connection: mysql, SQL: select `name`, `payload` from `settings` where `group` = developer) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 1045): SQLSTATE[HY000] [1045] Access denied for user 'forge'@'localhost' (using password: YES) (Connection: mysql, SQL: select `name`, `payload` from `settings` where `group` = developer) at D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:829)
[stacktrace]
#0 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('select `name`, ...', Array, Object(Closure))
#1 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(431): Illuminate\\Database\\Connection->run('select `name`, ...', Array, Object(Closure))
#2 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2914): Illuminate\\Database\\Connection->select('select `name`, ...', Array, true)
#3 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2902): Illuminate\\Database\\Query\\Builder->runSelect()
#4 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3456): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#5 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2903): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#6 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(739): Illuminate\\Database\\Query\\Builder->get(Array)
#7 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(723): Illuminate\\Database\\Eloquent\\Builder->getModels(Array)
#8 D:\\Workstation\\hoomi\\vendor\\spatie\\laravel-settings\\src\\SettingsRepositories\\DatabaseSettingsRepository.php(28): Illuminate\\Database\\Eloquent\\Builder->get(Array)
#9 D:\\Workstation\\hoomi\\vendor\\spatie\\laravel-settings\\src\\SettingsMapper.php(86): Spatie\\LaravelSettings\\SettingsRepositories\\DatabaseSettingsRepository->getPropertiesInGroup('developer')
#10 D:\\Workstation\\hoomi\\vendor\\spatie\\laravel-settings\\src\\SettingsMapper.php(37): Spatie\\LaravelSettings\\SettingsMapper->fetchProperties('App\\\\Settings\\\\De...', Object(Illuminate\\Support\\Collection))
#11 D:\\Workstation\\hoomi\\vendor\\spatie\\laravel-settings\\src\\Settings.php(277): Spatie\\LaravelSettings\\SettingsMapper->load('App\\\\Settings\\\\De...')
#12 D:\\Workstation\\hoomi\\vendor\\spatie\\laravel-settings\\src\\Settings.php(101): Spatie\\LaravelSettings\\Settings->loadValues()
#13 D:\\Workstation\\hoomi\\app\\Providers\\AppServiceProvider.php(60): Spatie\\LaravelSettings\\Settings->__get('debug_mode')
#14 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Tasawk\\Providers\\AppServiceProvider->boot()
#15 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#16 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#17 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#18 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#19 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1059): Illuminate\\Container\\Container->call(Array)
#20 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1040): Illuminate\\Foundation\\Application->bootProvider(Object(Tasawk\\Providers\\AppServiceProvider))
#21 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(Tasawk\\Providers\\AppServiceProvider), 63)
#22 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1041): array_walk(Array, Object(Closure))
#23 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#24 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(286): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#25 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(447): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#26 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(199): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#27 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#28 {main}

[previous exception] [object] (PDOException(code: 1045): SQLSTATE[HY000] [1045] Access denied for user 'forge'@'localhost' (using password: YES) at D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php:65)
[stacktrace]
#0 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(65): PDO->__construct('mysql:host=127....', 'forge', '', Array)
#1 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(45): Illuminate\\Database\\Connectors\\Connector->createPdoConnection('mysql:host=127....', 'forge', '', Array)
#2 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\MySqlConnector.php(24): Illuminate\\Database\\Connectors\\Connector->createConnection('mysql:host=127....', Array, Array)
#3 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(184): Illuminate\\Database\\Connectors\\MySqlConnector->connect(Array)
#4 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->Illuminate\\Database\\Connectors\\{closure}()
#5 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1339): call_user_func(Object(Closure))
#6 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1375): Illuminate\\Database\\Connection->getPdo()
#7 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(528): Illuminate\\Database\\Connection->getReadPdo()
#8 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(423): Illuminate\\Database\\Connection->getPdoForSelect(true)
#9 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(816): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select `name`, ...', Array)
#10 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('select `name`, ...', Array, Object(Closure))
#11 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(431): Illuminate\\Database\\Connection->run('select `name`, ...', Array, Object(Closure))
#12 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2914): Illuminate\\Database\\Connection->select('select `name`, ...', Array, true)
#13 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2902): Illuminate\\Database\\Query\\Builder->runSelect()
#14 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3456): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#15 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2903): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#16 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(739): Illuminate\\Database\\Query\\Builder->get(Array)
#17 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(723): Illuminate\\Database\\Eloquent\\Builder->getModels(Array)
#18 D:\\Workstation\\hoomi\\vendor\\spatie\\laravel-settings\\src\\SettingsRepositories\\DatabaseSettingsRepository.php(28): Illuminate\\Database\\Eloquent\\Builder->get(Array)
#19 D:\\Workstation\\hoomi\\vendor\\spatie\\laravel-settings\\src\\SettingsMapper.php(86): Spatie\\LaravelSettings\\SettingsRepositories\\DatabaseSettingsRepository->getPropertiesInGroup('developer')
#20 D:\\Workstation\\hoomi\\vendor\\spatie\\laravel-settings\\src\\SettingsMapper.php(37): Spatie\\LaravelSettings\\SettingsMapper->fetchProperties('App\\\\Settings\\\\De...', Object(Illuminate\\Support\\Collection))
#21 D:\\Workstation\\hoomi\\vendor\\spatie\\laravel-settings\\src\\Settings.php(277): Spatie\\LaravelSettings\\SettingsMapper->load('App\\\\Settings\\\\De...')
#22 D:\\Workstation\\hoomi\\vendor\\spatie\\laravel-settings\\src\\Settings.php(101): Spatie\\LaravelSettings\\Settings->loadValues()
#23 D:\\Workstation\\hoomi\\app\\Providers\\AppServiceProvider.php(60): Spatie\\LaravelSettings\\Settings->__get('debug_mode')
#24 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Tasawk\\Providers\\AppServiceProvider->boot()
#25 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#26 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#27 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#28 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#29 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1059): Illuminate\\Container\\Container->call(Array)
#30 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1040): Illuminate\\Foundation\\Application->bootProvider(Object(Tasawk\\Providers\\AppServiceProvider))
#31 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(Tasawk\\Providers\\AppServiceProvider), 63)
#32 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1041): array_walk(Array, Object(Closure))
#33 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#34 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(286): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#35 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(447): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#36 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(199): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#37 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#38 {main}
"} 
[2025-05-28 09:28:12] production.ERROR: SQLSTATE[HY000] [1045] Access denied for user 'forge'@'localhost' (using password: YES) (Connection: mysql, SQL: select `name`, `payload` from `settings` where `group` = developer) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 1045): SQLSTATE[HY000] [1045] Access denied for user 'forge'@'localhost' (using password: YES) (Connection: mysql, SQL: select `name`, `payload` from `settings` where `group` = developer) at D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:829)
[stacktrace]
#0 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('select `name`, ...', Array, Object(Closure))
#1 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(431): Illuminate\\Database\\Connection->run('select `name`, ...', Array, Object(Closure))
#2 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2914): Illuminate\\Database\\Connection->select('select `name`, ...', Array, true)
#3 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2902): Illuminate\\Database\\Query\\Builder->runSelect()
#4 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3456): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#5 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2903): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#6 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(739): Illuminate\\Database\\Query\\Builder->get(Array)
#7 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(723): Illuminate\\Database\\Eloquent\\Builder->getModels(Array)
#8 D:\\Workstation\\hoomi\\vendor\\spatie\\laravel-settings\\src\\SettingsRepositories\\DatabaseSettingsRepository.php(28): Illuminate\\Database\\Eloquent\\Builder->get(Array)
#9 D:\\Workstation\\hoomi\\vendor\\spatie\\laravel-settings\\src\\SettingsMapper.php(86): Spatie\\LaravelSettings\\SettingsRepositories\\DatabaseSettingsRepository->getPropertiesInGroup('developer')
#10 D:\\Workstation\\hoomi\\vendor\\spatie\\laravel-settings\\src\\SettingsMapper.php(37): Spatie\\LaravelSettings\\SettingsMapper->fetchProperties('App\\\\Settings\\\\De...', Object(Illuminate\\Support\\Collection))
#11 D:\\Workstation\\hoomi\\vendor\\spatie\\laravel-settings\\src\\Settings.php(277): Spatie\\LaravelSettings\\SettingsMapper->load('App\\\\Settings\\\\De...')
#12 D:\\Workstation\\hoomi\\vendor\\spatie\\laravel-settings\\src\\Settings.php(101): Spatie\\LaravelSettings\\Settings->loadValues()
#13 D:\\Workstation\\hoomi\\app\\Providers\\AppServiceProvider.php(60): Spatie\\LaravelSettings\\Settings->__get('debug_mode')
#14 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Tasawk\\Providers\\AppServiceProvider->boot()
#15 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#16 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#17 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#18 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#19 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1059): Illuminate\\Container\\Container->call(Array)
#20 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1040): Illuminate\\Foundation\\Application->bootProvider(Object(Tasawk\\Providers\\AppServiceProvider))
#21 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(Tasawk\\Providers\\AppServiceProvider), 63)
#22 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1041): array_walk(Array, Object(Closure))
#23 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#24 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(286): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#25 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(447): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#26 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(199): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#27 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#28 {main}

[previous exception] [object] (PDOException(code: 1045): SQLSTATE[HY000] [1045] Access denied for user 'forge'@'localhost' (using password: YES) at D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php:65)
[stacktrace]
#0 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(65): PDO->__construct('mysql:host=127....', 'forge', '', Array)
#1 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(45): Illuminate\\Database\\Connectors\\Connector->createPdoConnection('mysql:host=127....', 'forge', '', Array)
#2 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\MySqlConnector.php(24): Illuminate\\Database\\Connectors\\Connector->createConnection('mysql:host=127....', Array, Array)
#3 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(184): Illuminate\\Database\\Connectors\\MySqlConnector->connect(Array)
#4 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->Illuminate\\Database\\Connectors\\{closure}()
#5 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1339): call_user_func(Object(Closure))
#6 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1375): Illuminate\\Database\\Connection->getPdo()
#7 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(528): Illuminate\\Database\\Connection->getReadPdo()
#8 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(423): Illuminate\\Database\\Connection->getPdoForSelect(true)
#9 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(816): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select `name`, ...', Array)
#10 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('select `name`, ...', Array, Object(Closure))
#11 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(431): Illuminate\\Database\\Connection->run('select `name`, ...', Array, Object(Closure))
#12 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2914): Illuminate\\Database\\Connection->select('select `name`, ...', Array, true)
#13 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2902): Illuminate\\Database\\Query\\Builder->runSelect()
#14 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3456): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#15 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2903): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#16 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(739): Illuminate\\Database\\Query\\Builder->get(Array)
#17 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(723): Illuminate\\Database\\Eloquent\\Builder->getModels(Array)
#18 D:\\Workstation\\hoomi\\vendor\\spatie\\laravel-settings\\src\\SettingsRepositories\\DatabaseSettingsRepository.php(28): Illuminate\\Database\\Eloquent\\Builder->get(Array)
#19 D:\\Workstation\\hoomi\\vendor\\spatie\\laravel-settings\\src\\SettingsMapper.php(86): Spatie\\LaravelSettings\\SettingsRepositories\\DatabaseSettingsRepository->getPropertiesInGroup('developer')
#20 D:\\Workstation\\hoomi\\vendor\\spatie\\laravel-settings\\src\\SettingsMapper.php(37): Spatie\\LaravelSettings\\SettingsMapper->fetchProperties('App\\\\Settings\\\\De...', Object(Illuminate\\Support\\Collection))
#21 D:\\Workstation\\hoomi\\vendor\\spatie\\laravel-settings\\src\\Settings.php(277): Spatie\\LaravelSettings\\SettingsMapper->load('App\\\\Settings\\\\De...')
#22 D:\\Workstation\\hoomi\\vendor\\spatie\\laravel-settings\\src\\Settings.php(101): Spatie\\LaravelSettings\\Settings->loadValues()
#23 D:\\Workstation\\hoomi\\app\\Providers\\AppServiceProvider.php(60): Spatie\\LaravelSettings\\Settings->__get('debug_mode')
#24 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Tasawk\\Providers\\AppServiceProvider->boot()
#25 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#26 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#27 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#28 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#29 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1059): Illuminate\\Container\\Container->call(Array)
#30 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1040): Illuminate\\Foundation\\Application->bootProvider(Object(Tasawk\\Providers\\AppServiceProvider))
#31 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(Tasawk\\Providers\\AppServiceProvider), 63)
#32 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1041): array_walk(Array, Object(Closure))
#33 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#34 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(286): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#35 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(447): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#36 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(199): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#37 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#38 {main}
"} 
[2025-05-28 09:29:12] production.ERROR: SQLSTATE[HY000] [1045] Access denied for user 'forge'@'localhost' (using password: YES) (Connection: mysql, SQL: select `name`, `payload` from `settings` where `group` = developer) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 1045): SQLSTATE[HY000] [1045] Access denied for user 'forge'@'localhost' (using password: YES) (Connection: mysql, SQL: select `name`, `payload` from `settings` where `group` = developer) at D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:829)
[stacktrace]
#0 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('select `name`, ...', Array, Object(Closure))
#1 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(431): Illuminate\\Database\\Connection->run('select `name`, ...', Array, Object(Closure))
#2 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2914): Illuminate\\Database\\Connection->select('select `name`, ...', Array, true)
#3 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2902): Illuminate\\Database\\Query\\Builder->runSelect()
#4 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3456): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#5 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2903): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#6 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(739): Illuminate\\Database\\Query\\Builder->get(Array)
#7 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(723): Illuminate\\Database\\Eloquent\\Builder->getModels(Array)
#8 D:\\Workstation\\hoomi\\vendor\\spatie\\laravel-settings\\src\\SettingsRepositories\\DatabaseSettingsRepository.php(28): Illuminate\\Database\\Eloquent\\Builder->get(Array)
#9 D:\\Workstation\\hoomi\\vendor\\spatie\\laravel-settings\\src\\SettingsMapper.php(86): Spatie\\LaravelSettings\\SettingsRepositories\\DatabaseSettingsRepository->getPropertiesInGroup('developer')
#10 D:\\Workstation\\hoomi\\vendor\\spatie\\laravel-settings\\src\\SettingsMapper.php(37): Spatie\\LaravelSettings\\SettingsMapper->fetchProperties('App\\\\Settings\\\\De...', Object(Illuminate\\Support\\Collection))
#11 D:\\Workstation\\hoomi\\vendor\\spatie\\laravel-settings\\src\\Settings.php(277): Spatie\\LaravelSettings\\SettingsMapper->load('App\\\\Settings\\\\De...')
#12 D:\\Workstation\\hoomi\\vendor\\spatie\\laravel-settings\\src\\Settings.php(101): Spatie\\LaravelSettings\\Settings->loadValues()
#13 D:\\Workstation\\hoomi\\app\\Providers\\AppServiceProvider.php(60): Spatie\\LaravelSettings\\Settings->__get('debug_mode')
#14 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Tasawk\\Providers\\AppServiceProvider->boot()
#15 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#16 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#17 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#18 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#19 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1059): Illuminate\\Container\\Container->call(Array)
#20 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1040): Illuminate\\Foundation\\Application->bootProvider(Object(Tasawk\\Providers\\AppServiceProvider))
#21 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(Tasawk\\Providers\\AppServiceProvider), 63)
#22 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1041): array_walk(Array, Object(Closure))
#23 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#24 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(286): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#25 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(447): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#26 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(199): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#27 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#28 {main}

[previous exception] [object] (PDOException(code: 1045): SQLSTATE[HY000] [1045] Access denied for user 'forge'@'localhost' (using password: YES) at D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php:65)
[stacktrace]
#0 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(65): PDO->__construct('mysql:host=127....', 'forge', '', Array)
#1 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(45): Illuminate\\Database\\Connectors\\Connector->createPdoConnection('mysql:host=127....', 'forge', '', Array)
#2 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\MySqlConnector.php(24): Illuminate\\Database\\Connectors\\Connector->createConnection('mysql:host=127....', Array, Array)
#3 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(184): Illuminate\\Database\\Connectors\\MySqlConnector->connect(Array)
#4 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->Illuminate\\Database\\Connectors\\{closure}()
#5 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1339): call_user_func(Object(Closure))
#6 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1375): Illuminate\\Database\\Connection->getPdo()
#7 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(528): Illuminate\\Database\\Connection->getReadPdo()
#8 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(423): Illuminate\\Database\\Connection->getPdoForSelect(true)
#9 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(816): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select `name`, ...', Array)
#10 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('select `name`, ...', Array, Object(Closure))
#11 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(431): Illuminate\\Database\\Connection->run('select `name`, ...', Array, Object(Closure))
#12 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2914): Illuminate\\Database\\Connection->select('select `name`, ...', Array, true)
#13 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2902): Illuminate\\Database\\Query\\Builder->runSelect()
#14 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3456): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#15 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2903): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#16 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(739): Illuminate\\Database\\Query\\Builder->get(Array)
#17 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(723): Illuminate\\Database\\Eloquent\\Builder->getModels(Array)
#18 D:\\Workstation\\hoomi\\vendor\\spatie\\laravel-settings\\src\\SettingsRepositories\\DatabaseSettingsRepository.php(28): Illuminate\\Database\\Eloquent\\Builder->get(Array)
#19 D:\\Workstation\\hoomi\\vendor\\spatie\\laravel-settings\\src\\SettingsMapper.php(86): Spatie\\LaravelSettings\\SettingsRepositories\\DatabaseSettingsRepository->getPropertiesInGroup('developer')
#20 D:\\Workstation\\hoomi\\vendor\\spatie\\laravel-settings\\src\\SettingsMapper.php(37): Spatie\\LaravelSettings\\SettingsMapper->fetchProperties('App\\\\Settings\\\\De...', Object(Illuminate\\Support\\Collection))
#21 D:\\Workstation\\hoomi\\vendor\\spatie\\laravel-settings\\src\\Settings.php(277): Spatie\\LaravelSettings\\SettingsMapper->load('App\\\\Settings\\\\De...')
#22 D:\\Workstation\\hoomi\\vendor\\spatie\\laravel-settings\\src\\Settings.php(101): Spatie\\LaravelSettings\\Settings->loadValues()
#23 D:\\Workstation\\hoomi\\app\\Providers\\AppServiceProvider.php(60): Spatie\\LaravelSettings\\Settings->__get('debug_mode')
#24 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Tasawk\\Providers\\AppServiceProvider->boot()
#25 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#26 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#27 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#28 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#29 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1059): Illuminate\\Container\\Container->call(Array)
#30 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1040): Illuminate\\Foundation\\Application->bootProvider(Object(Tasawk\\Providers\\AppServiceProvider))
#31 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(Tasawk\\Providers\\AppServiceProvider), 63)
#32 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1041): array_walk(Array, Object(Closure))
#33 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#34 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(286): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#35 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(447): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#36 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(199): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#37 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#38 {main}
"} 
[2025-05-28 09:29:12] production.ERROR: SQLSTATE[HY000] [1045] Access denied for user 'forge'@'localhost' (using password: YES) (Connection: mysql, SQL: select `name`, `payload` from `settings` where `group` = developer) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 1045): SQLSTATE[HY000] [1045] Access denied for user 'forge'@'localhost' (using password: YES) (Connection: mysql, SQL: select `name`, `payload` from `settings` where `group` = developer) at D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:829)
[stacktrace]
#0 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('select `name`, ...', Array, Object(Closure))
#1 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(431): Illuminate\\Database\\Connection->run('select `name`, ...', Array, Object(Closure))
#2 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2914): Illuminate\\Database\\Connection->select('select `name`, ...', Array, true)
#3 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2902): Illuminate\\Database\\Query\\Builder->runSelect()
#4 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3456): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#5 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2903): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#6 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(739): Illuminate\\Database\\Query\\Builder->get(Array)
#7 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(723): Illuminate\\Database\\Eloquent\\Builder->getModels(Array)
#8 D:\\Workstation\\hoomi\\vendor\\spatie\\laravel-settings\\src\\SettingsRepositories\\DatabaseSettingsRepository.php(28): Illuminate\\Database\\Eloquent\\Builder->get(Array)
#9 D:\\Workstation\\hoomi\\vendor\\spatie\\laravel-settings\\src\\SettingsMapper.php(86): Spatie\\LaravelSettings\\SettingsRepositories\\DatabaseSettingsRepository->getPropertiesInGroup('developer')
#10 D:\\Workstation\\hoomi\\vendor\\spatie\\laravel-settings\\src\\SettingsMapper.php(37): Spatie\\LaravelSettings\\SettingsMapper->fetchProperties('App\\\\Settings\\\\De...', Object(Illuminate\\Support\\Collection))
#11 D:\\Workstation\\hoomi\\vendor\\spatie\\laravel-settings\\src\\Settings.php(277): Spatie\\LaravelSettings\\SettingsMapper->load('App\\\\Settings\\\\De...')
#12 D:\\Workstation\\hoomi\\vendor\\spatie\\laravel-settings\\src\\Settings.php(101): Spatie\\LaravelSettings\\Settings->loadValues()
#13 D:\\Workstation\\hoomi\\app\\Providers\\AppServiceProvider.php(60): Spatie\\LaravelSettings\\Settings->__get('debug_mode')
#14 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Tasawk\\Providers\\AppServiceProvider->boot()
#15 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#16 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#17 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#18 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#19 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1059): Illuminate\\Container\\Container->call(Array)
#20 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1040): Illuminate\\Foundation\\Application->bootProvider(Object(Tasawk\\Providers\\AppServiceProvider))
#21 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(Tasawk\\Providers\\AppServiceProvider), 63)
#22 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1041): array_walk(Array, Object(Closure))
#23 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#24 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(286): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#25 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(447): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#26 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(199): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#27 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#28 {main}

[previous exception] [object] (PDOException(code: 1045): SQLSTATE[HY000] [1045] Access denied for user 'forge'@'localhost' (using password: YES) at D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php:65)
[stacktrace]
#0 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(65): PDO->__construct('mysql:host=127....', 'forge', '', Array)
#1 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(45): Illuminate\\Database\\Connectors\\Connector->createPdoConnection('mysql:host=127....', 'forge', '', Array)
#2 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\MySqlConnector.php(24): Illuminate\\Database\\Connectors\\Connector->createConnection('mysql:host=127....', Array, Array)
#3 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(184): Illuminate\\Database\\Connectors\\MySqlConnector->connect(Array)
#4 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->Illuminate\\Database\\Connectors\\{closure}()
#5 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1339): call_user_func(Object(Closure))
#6 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1375): Illuminate\\Database\\Connection->getPdo()
#7 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(528): Illuminate\\Database\\Connection->getReadPdo()
#8 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(423): Illuminate\\Database\\Connection->getPdoForSelect(true)
#9 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(816): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select `name`, ...', Array)
#10 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('select `name`, ...', Array, Object(Closure))
#11 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(431): Illuminate\\Database\\Connection->run('select `name`, ...', Array, Object(Closure))
#12 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2914): Illuminate\\Database\\Connection->select('select `name`, ...', Array, true)
#13 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2902): Illuminate\\Database\\Query\\Builder->runSelect()
#14 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3456): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#15 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2903): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#16 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(739): Illuminate\\Database\\Query\\Builder->get(Array)
#17 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(723): Illuminate\\Database\\Eloquent\\Builder->getModels(Array)
#18 D:\\Workstation\\hoomi\\vendor\\spatie\\laravel-settings\\src\\SettingsRepositories\\DatabaseSettingsRepository.php(28): Illuminate\\Database\\Eloquent\\Builder->get(Array)
#19 D:\\Workstation\\hoomi\\vendor\\spatie\\laravel-settings\\src\\SettingsMapper.php(86): Spatie\\LaravelSettings\\SettingsRepositories\\DatabaseSettingsRepository->getPropertiesInGroup('developer')
#20 D:\\Workstation\\hoomi\\vendor\\spatie\\laravel-settings\\src\\SettingsMapper.php(37): Spatie\\LaravelSettings\\SettingsMapper->fetchProperties('App\\\\Settings\\\\De...', Object(Illuminate\\Support\\Collection))
#21 D:\\Workstation\\hoomi\\vendor\\spatie\\laravel-settings\\src\\Settings.php(277): Spatie\\LaravelSettings\\SettingsMapper->load('App\\\\Settings\\\\De...')
#22 D:\\Workstation\\hoomi\\vendor\\spatie\\laravel-settings\\src\\Settings.php(101): Spatie\\LaravelSettings\\Settings->loadValues()
#23 D:\\Workstation\\hoomi\\app\\Providers\\AppServiceProvider.php(60): Spatie\\LaravelSettings\\Settings->__get('debug_mode')
#24 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Tasawk\\Providers\\AppServiceProvider->boot()
#25 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#26 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#27 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#28 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#29 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1059): Illuminate\\Container\\Container->call(Array)
#30 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1040): Illuminate\\Foundation\\Application->bootProvider(Object(Tasawk\\Providers\\AppServiceProvider))
#31 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(Tasawk\\Providers\\AppServiceProvider), 63)
#32 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1041): array_walk(Array, Object(Closure))
#33 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#34 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(286): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#35 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(447): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#36 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(199): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#37 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#38 {main}
"} 
[2025-05-28 09:30:12] production.ERROR: SQLSTATE[HY000] [1045] Access denied for user 'forge'@'localhost' (using password: YES) (Connection: mysql, SQL: select `name`, `payload` from `settings` where `group` = developer) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 1045): SQLSTATE[HY000] [1045] Access denied for user 'forge'@'localhost' (using password: YES) (Connection: mysql, SQL: select `name`, `payload` from `settings` where `group` = developer) at D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:829)
[stacktrace]
#0 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('select `name`, ...', Array, Object(Closure))
#1 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(431): Illuminate\\Database\\Connection->run('select `name`, ...', Array, Object(Closure))
#2 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2914): Illuminate\\Database\\Connection->select('select `name`, ...', Array, true)
#3 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2902): Illuminate\\Database\\Query\\Builder->runSelect()
#4 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3456): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#5 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2903): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#6 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(739): Illuminate\\Database\\Query\\Builder->get(Array)
#7 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(723): Illuminate\\Database\\Eloquent\\Builder->getModels(Array)
#8 D:\\Workstation\\hoomi\\vendor\\spatie\\laravel-settings\\src\\SettingsRepositories\\DatabaseSettingsRepository.php(28): Illuminate\\Database\\Eloquent\\Builder->get(Array)
#9 D:\\Workstation\\hoomi\\vendor\\spatie\\laravel-settings\\src\\SettingsMapper.php(86): Spatie\\LaravelSettings\\SettingsRepositories\\DatabaseSettingsRepository->getPropertiesInGroup('developer')
#10 D:\\Workstation\\hoomi\\vendor\\spatie\\laravel-settings\\src\\SettingsMapper.php(37): Spatie\\LaravelSettings\\SettingsMapper->fetchProperties('App\\\\Settings\\\\De...', Object(Illuminate\\Support\\Collection))
#11 D:\\Workstation\\hoomi\\vendor\\spatie\\laravel-settings\\src\\Settings.php(277): Spatie\\LaravelSettings\\SettingsMapper->load('App\\\\Settings\\\\De...')
#12 D:\\Workstation\\hoomi\\vendor\\spatie\\laravel-settings\\src\\Settings.php(101): Spatie\\LaravelSettings\\Settings->loadValues()
#13 D:\\Workstation\\hoomi\\app\\Providers\\AppServiceProvider.php(60): Spatie\\LaravelSettings\\Settings->__get('debug_mode')
#14 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Tasawk\\Providers\\AppServiceProvider->boot()
#15 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#16 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#17 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#18 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#19 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1059): Illuminate\\Container\\Container->call(Array)
#20 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1040): Illuminate\\Foundation\\Application->bootProvider(Object(Tasawk\\Providers\\AppServiceProvider))
#21 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(Tasawk\\Providers\\AppServiceProvider), 63)
#22 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1041): array_walk(Array, Object(Closure))
#23 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#24 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(286): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#25 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(447): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#26 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(199): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#27 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#28 {main}

[previous exception] [object] (PDOException(code: 1045): SQLSTATE[HY000] [1045] Access denied for user 'forge'@'localhost' (using password: YES) at D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php:65)
[stacktrace]
#0 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(65): PDO->__construct('mysql:host=127....', 'forge', '', Array)
#1 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(45): Illuminate\\Database\\Connectors\\Connector->createPdoConnection('mysql:host=127....', 'forge', '', Array)
#2 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\MySqlConnector.php(24): Illuminate\\Database\\Connectors\\Connector->createConnection('mysql:host=127....', Array, Array)
#3 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(184): Illuminate\\Database\\Connectors\\MySqlConnector->connect(Array)
#4 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->Illuminate\\Database\\Connectors\\{closure}()
#5 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1339): call_user_func(Object(Closure))
#6 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1375): Illuminate\\Database\\Connection->getPdo()
#7 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(528): Illuminate\\Database\\Connection->getReadPdo()
#8 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(423): Illuminate\\Database\\Connection->getPdoForSelect(true)
#9 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(816): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select `name`, ...', Array)
#10 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('select `name`, ...', Array, Object(Closure))
#11 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(431): Illuminate\\Database\\Connection->run('select `name`, ...', Array, Object(Closure))
#12 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2914): Illuminate\\Database\\Connection->select('select `name`, ...', Array, true)
#13 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2902): Illuminate\\Database\\Query\\Builder->runSelect()
#14 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3456): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#15 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2903): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#16 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(739): Illuminate\\Database\\Query\\Builder->get(Array)
#17 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(723): Illuminate\\Database\\Eloquent\\Builder->getModels(Array)
#18 D:\\Workstation\\hoomi\\vendor\\spatie\\laravel-settings\\src\\SettingsRepositories\\DatabaseSettingsRepository.php(28): Illuminate\\Database\\Eloquent\\Builder->get(Array)
#19 D:\\Workstation\\hoomi\\vendor\\spatie\\laravel-settings\\src\\SettingsMapper.php(86): Spatie\\LaravelSettings\\SettingsRepositories\\DatabaseSettingsRepository->getPropertiesInGroup('developer')
#20 D:\\Workstation\\hoomi\\vendor\\spatie\\laravel-settings\\src\\SettingsMapper.php(37): Spatie\\LaravelSettings\\SettingsMapper->fetchProperties('App\\\\Settings\\\\De...', Object(Illuminate\\Support\\Collection))
#21 D:\\Workstation\\hoomi\\vendor\\spatie\\laravel-settings\\src\\Settings.php(277): Spatie\\LaravelSettings\\SettingsMapper->load('App\\\\Settings\\\\De...')
#22 D:\\Workstation\\hoomi\\vendor\\spatie\\laravel-settings\\src\\Settings.php(101): Spatie\\LaravelSettings\\Settings->loadValues()
#23 D:\\Workstation\\hoomi\\app\\Providers\\AppServiceProvider.php(60): Spatie\\LaravelSettings\\Settings->__get('debug_mode')
#24 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Tasawk\\Providers\\AppServiceProvider->boot()
#25 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#26 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#27 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#28 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#29 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1059): Illuminate\\Container\\Container->call(Array)
#30 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1040): Illuminate\\Foundation\\Application->bootProvider(Object(Tasawk\\Providers\\AppServiceProvider))
#31 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(Tasawk\\Providers\\AppServiceProvider), 63)
#32 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1041): array_walk(Array, Object(Closure))
#33 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#34 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(286): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#35 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(447): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#36 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(199): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#37 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#38 {main}
"} 
[2025-05-28 09:30:12] production.ERROR: SQLSTATE[HY000] [1045] Access denied for user 'forge'@'localhost' (using password: YES) (Connection: mysql, SQL: select `name`, `payload` from `settings` where `group` = developer) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 1045): SQLSTATE[HY000] [1045] Access denied for user 'forge'@'localhost' (using password: YES) (Connection: mysql, SQL: select `name`, `payload` from `settings` where `group` = developer) at D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:829)
[stacktrace]
#0 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('select `name`, ...', Array, Object(Closure))
#1 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(431): Illuminate\\Database\\Connection->run('select `name`, ...', Array, Object(Closure))
#2 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2914): Illuminate\\Database\\Connection->select('select `name`, ...', Array, true)
#3 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2902): Illuminate\\Database\\Query\\Builder->runSelect()
#4 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3456): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#5 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2903): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#6 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(739): Illuminate\\Database\\Query\\Builder->get(Array)
#7 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(723): Illuminate\\Database\\Eloquent\\Builder->getModels(Array)
#8 D:\\Workstation\\hoomi\\vendor\\spatie\\laravel-settings\\src\\SettingsRepositories\\DatabaseSettingsRepository.php(28): Illuminate\\Database\\Eloquent\\Builder->get(Array)
#9 D:\\Workstation\\hoomi\\vendor\\spatie\\laravel-settings\\src\\SettingsMapper.php(86): Spatie\\LaravelSettings\\SettingsRepositories\\DatabaseSettingsRepository->getPropertiesInGroup('developer')
#10 D:\\Workstation\\hoomi\\vendor\\spatie\\laravel-settings\\src\\SettingsMapper.php(37): Spatie\\LaravelSettings\\SettingsMapper->fetchProperties('App\\\\Settings\\\\De...', Object(Illuminate\\Support\\Collection))
#11 D:\\Workstation\\hoomi\\vendor\\spatie\\laravel-settings\\src\\Settings.php(277): Spatie\\LaravelSettings\\SettingsMapper->load('App\\\\Settings\\\\De...')
#12 D:\\Workstation\\hoomi\\vendor\\spatie\\laravel-settings\\src\\Settings.php(101): Spatie\\LaravelSettings\\Settings->loadValues()
#13 D:\\Workstation\\hoomi\\app\\Providers\\AppServiceProvider.php(60): Spatie\\LaravelSettings\\Settings->__get('debug_mode')
#14 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Tasawk\\Providers\\AppServiceProvider->boot()
#15 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#16 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#17 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#18 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#19 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1059): Illuminate\\Container\\Container->call(Array)
#20 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1040): Illuminate\\Foundation\\Application->bootProvider(Object(Tasawk\\Providers\\AppServiceProvider))
#21 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(Tasawk\\Providers\\AppServiceProvider), 63)
#22 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1041): array_walk(Array, Object(Closure))
#23 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#24 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(286): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#25 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(447): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#26 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(199): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#27 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#28 {main}

[previous exception] [object] (PDOException(code: 1045): SQLSTATE[HY000] [1045] Access denied for user 'forge'@'localhost' (using password: YES) at D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php:65)
[stacktrace]
#0 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(65): PDO->__construct('mysql:host=127....', 'forge', '', Array)
#1 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(45): Illuminate\\Database\\Connectors\\Connector->createPdoConnection('mysql:host=127....', 'forge', '', Array)
#2 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\MySqlConnector.php(24): Illuminate\\Database\\Connectors\\Connector->createConnection('mysql:host=127....', Array, Array)
#3 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(184): Illuminate\\Database\\Connectors\\MySqlConnector->connect(Array)
#4 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->Illuminate\\Database\\Connectors\\{closure}()
#5 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1339): call_user_func(Object(Closure))
#6 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1375): Illuminate\\Database\\Connection->getPdo()
#7 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(528): Illuminate\\Database\\Connection->getReadPdo()
#8 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(423): Illuminate\\Database\\Connection->getPdoForSelect(true)
#9 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(816): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select `name`, ...', Array)
#10 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('select `name`, ...', Array, Object(Closure))
#11 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(431): Illuminate\\Database\\Connection->run('select `name`, ...', Array, Object(Closure))
#12 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2914): Illuminate\\Database\\Connection->select('select `name`, ...', Array, true)
#13 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2902): Illuminate\\Database\\Query\\Builder->runSelect()
#14 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3456): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#15 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2903): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#16 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(739): Illuminate\\Database\\Query\\Builder->get(Array)
#17 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(723): Illuminate\\Database\\Eloquent\\Builder->getModels(Array)
#18 D:\\Workstation\\hoomi\\vendor\\spatie\\laravel-settings\\src\\SettingsRepositories\\DatabaseSettingsRepository.php(28): Illuminate\\Database\\Eloquent\\Builder->get(Array)
#19 D:\\Workstation\\hoomi\\vendor\\spatie\\laravel-settings\\src\\SettingsMapper.php(86): Spatie\\LaravelSettings\\SettingsRepositories\\DatabaseSettingsRepository->getPropertiesInGroup('developer')
#20 D:\\Workstation\\hoomi\\vendor\\spatie\\laravel-settings\\src\\SettingsMapper.php(37): Spatie\\LaravelSettings\\SettingsMapper->fetchProperties('App\\\\Settings\\\\De...', Object(Illuminate\\Support\\Collection))
#21 D:\\Workstation\\hoomi\\vendor\\spatie\\laravel-settings\\src\\Settings.php(277): Spatie\\LaravelSettings\\SettingsMapper->load('App\\\\Settings\\\\De...')
#22 D:\\Workstation\\hoomi\\vendor\\spatie\\laravel-settings\\src\\Settings.php(101): Spatie\\LaravelSettings\\Settings->loadValues()
#23 D:\\Workstation\\hoomi\\app\\Providers\\AppServiceProvider.php(60): Spatie\\LaravelSettings\\Settings->__get('debug_mode')
#24 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Tasawk\\Providers\\AppServiceProvider->boot()
#25 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#26 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#27 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#28 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#29 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1059): Illuminate\\Container\\Container->call(Array)
#30 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1040): Illuminate\\Foundation\\Application->bootProvider(Object(Tasawk\\Providers\\AppServiceProvider))
#31 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(Tasawk\\Providers\\AppServiceProvider), 63)
#32 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1041): array_walk(Array, Object(Closure))
#33 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#34 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(286): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#35 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(447): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#36 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(199): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#37 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#38 {main}
"} 
[2025-05-28 09:31:11] production.ERROR: SQLSTATE[HY000] [1045] Access denied for user 'forge'@'localhost' (using password: YES) (Connection: mysql, SQL: select `name`, `payload` from `settings` where `group` = developer) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 1045): SQLSTATE[HY000] [1045] Access denied for user 'forge'@'localhost' (using password: YES) (Connection: mysql, SQL: select `name`, `payload` from `settings` where `group` = developer) at D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:829)
[stacktrace]
#0 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('select `name`, ...', Array, Object(Closure))
#1 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(431): Illuminate\\Database\\Connection->run('select `name`, ...', Array, Object(Closure))
#2 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2914): Illuminate\\Database\\Connection->select('select `name`, ...', Array, true)
#3 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2902): Illuminate\\Database\\Query\\Builder->runSelect()
#4 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3456): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#5 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2903): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#6 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(739): Illuminate\\Database\\Query\\Builder->get(Array)
#7 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(723): Illuminate\\Database\\Eloquent\\Builder->getModels(Array)
#8 D:\\Workstation\\hoomi\\vendor\\spatie\\laravel-settings\\src\\SettingsRepositories\\DatabaseSettingsRepository.php(28): Illuminate\\Database\\Eloquent\\Builder->get(Array)
#9 D:\\Workstation\\hoomi\\vendor\\spatie\\laravel-settings\\src\\SettingsMapper.php(86): Spatie\\LaravelSettings\\SettingsRepositories\\DatabaseSettingsRepository->getPropertiesInGroup('developer')
#10 D:\\Workstation\\hoomi\\vendor\\spatie\\laravel-settings\\src\\SettingsMapper.php(37): Spatie\\LaravelSettings\\SettingsMapper->fetchProperties('App\\\\Settings\\\\De...', Object(Illuminate\\Support\\Collection))
#11 D:\\Workstation\\hoomi\\vendor\\spatie\\laravel-settings\\src\\Settings.php(277): Spatie\\LaravelSettings\\SettingsMapper->load('App\\\\Settings\\\\De...')
#12 D:\\Workstation\\hoomi\\vendor\\spatie\\laravel-settings\\src\\Settings.php(101): Spatie\\LaravelSettings\\Settings->loadValues()
#13 D:\\Workstation\\hoomi\\app\\Providers\\AppServiceProvider.php(60): Spatie\\LaravelSettings\\Settings->__get('debug_mode')
#14 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Tasawk\\Providers\\AppServiceProvider->boot()
#15 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#16 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#17 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#18 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#19 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1059): Illuminate\\Container\\Container->call(Array)
#20 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1040): Illuminate\\Foundation\\Application->bootProvider(Object(Tasawk\\Providers\\AppServiceProvider))
#21 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(Tasawk\\Providers\\AppServiceProvider), 63)
#22 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1041): array_walk(Array, Object(Closure))
#23 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#24 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(286): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#25 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(447): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#26 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(199): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#27 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#28 {main}

[previous exception] [object] (PDOException(code: 1045): SQLSTATE[HY000] [1045] Access denied for user 'forge'@'localhost' (using password: YES) at D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php:65)
[stacktrace]
#0 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(65): PDO->__construct('mysql:host=127....', 'forge', '', Array)
#1 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(45): Illuminate\\Database\\Connectors\\Connector->createPdoConnection('mysql:host=127....', 'forge', '', Array)
#2 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\MySqlConnector.php(24): Illuminate\\Database\\Connectors\\Connector->createConnection('mysql:host=127....', Array, Array)
#3 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(184): Illuminate\\Database\\Connectors\\MySqlConnector->connect(Array)
#4 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->Illuminate\\Database\\Connectors\\{closure}()
#5 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1339): call_user_func(Object(Closure))
#6 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1375): Illuminate\\Database\\Connection->getPdo()
#7 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(528): Illuminate\\Database\\Connection->getReadPdo()
#8 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(423): Illuminate\\Database\\Connection->getPdoForSelect(true)
#9 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(816): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select `name`, ...', Array)
#10 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('select `name`, ...', Array, Object(Closure))
#11 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(431): Illuminate\\Database\\Connection->run('select `name`, ...', Array, Object(Closure))
#12 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2914): Illuminate\\Database\\Connection->select('select `name`, ...', Array, true)
#13 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2902): Illuminate\\Database\\Query\\Builder->runSelect()
#14 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3456): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#15 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2903): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#16 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(739): Illuminate\\Database\\Query\\Builder->get(Array)
#17 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(723): Illuminate\\Database\\Eloquent\\Builder->getModels(Array)
#18 D:\\Workstation\\hoomi\\vendor\\spatie\\laravel-settings\\src\\SettingsRepositories\\DatabaseSettingsRepository.php(28): Illuminate\\Database\\Eloquent\\Builder->get(Array)
#19 D:\\Workstation\\hoomi\\vendor\\spatie\\laravel-settings\\src\\SettingsMapper.php(86): Spatie\\LaravelSettings\\SettingsRepositories\\DatabaseSettingsRepository->getPropertiesInGroup('developer')
#20 D:\\Workstation\\hoomi\\vendor\\spatie\\laravel-settings\\src\\SettingsMapper.php(37): Spatie\\LaravelSettings\\SettingsMapper->fetchProperties('App\\\\Settings\\\\De...', Object(Illuminate\\Support\\Collection))
#21 D:\\Workstation\\hoomi\\vendor\\spatie\\laravel-settings\\src\\Settings.php(277): Spatie\\LaravelSettings\\SettingsMapper->load('App\\\\Settings\\\\De...')
#22 D:\\Workstation\\hoomi\\vendor\\spatie\\laravel-settings\\src\\Settings.php(101): Spatie\\LaravelSettings\\Settings->loadValues()
#23 D:\\Workstation\\hoomi\\app\\Providers\\AppServiceProvider.php(60): Spatie\\LaravelSettings\\Settings->__get('debug_mode')
#24 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Tasawk\\Providers\\AppServiceProvider->boot()
#25 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#26 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#27 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#28 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#29 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1059): Illuminate\\Container\\Container->call(Array)
#30 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1040): Illuminate\\Foundation\\Application->bootProvider(Object(Tasawk\\Providers\\AppServiceProvider))
#31 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(Tasawk\\Providers\\AppServiceProvider), 63)
#32 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1041): array_walk(Array, Object(Closure))
#33 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#34 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(286): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#35 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(447): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#36 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(199): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#37 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#38 {main}
"} 
[2025-05-28 09:31:11] production.ERROR: SQLSTATE[HY000] [1045] Access denied for user 'forge'@'localhost' (using password: YES) (Connection: mysql, SQL: select `name`, `payload` from `settings` where `group` = developer) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 1045): SQLSTATE[HY000] [1045] Access denied for user 'forge'@'localhost' (using password: YES) (Connection: mysql, SQL: select `name`, `payload` from `settings` where `group` = developer) at D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:829)
[stacktrace]
#0 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('select `name`, ...', Array, Object(Closure))
#1 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(431): Illuminate\\Database\\Connection->run('select `name`, ...', Array, Object(Closure))
#2 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2914): Illuminate\\Database\\Connection->select('select `name`, ...', Array, true)
#3 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2902): Illuminate\\Database\\Query\\Builder->runSelect()
#4 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3456): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#5 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2903): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#6 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(739): Illuminate\\Database\\Query\\Builder->get(Array)
#7 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(723): Illuminate\\Database\\Eloquent\\Builder->getModels(Array)
#8 D:\\Workstation\\hoomi\\vendor\\spatie\\laravel-settings\\src\\SettingsRepositories\\DatabaseSettingsRepository.php(28): Illuminate\\Database\\Eloquent\\Builder->get(Array)
#9 D:\\Workstation\\hoomi\\vendor\\spatie\\laravel-settings\\src\\SettingsMapper.php(86): Spatie\\LaravelSettings\\SettingsRepositories\\DatabaseSettingsRepository->getPropertiesInGroup('developer')
#10 D:\\Workstation\\hoomi\\vendor\\spatie\\laravel-settings\\src\\SettingsMapper.php(37): Spatie\\LaravelSettings\\SettingsMapper->fetchProperties('App\\\\Settings\\\\De...', Object(Illuminate\\Support\\Collection))
#11 D:\\Workstation\\hoomi\\vendor\\spatie\\laravel-settings\\src\\Settings.php(277): Spatie\\LaravelSettings\\SettingsMapper->load('App\\\\Settings\\\\De...')
#12 D:\\Workstation\\hoomi\\vendor\\spatie\\laravel-settings\\src\\Settings.php(101): Spatie\\LaravelSettings\\Settings->loadValues()
#13 D:\\Workstation\\hoomi\\app\\Providers\\AppServiceProvider.php(60): Spatie\\LaravelSettings\\Settings->__get('debug_mode')
#14 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Tasawk\\Providers\\AppServiceProvider->boot()
#15 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#16 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#17 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#18 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#19 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1059): Illuminate\\Container\\Container->call(Array)
#20 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1040): Illuminate\\Foundation\\Application->bootProvider(Object(Tasawk\\Providers\\AppServiceProvider))
#21 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(Tasawk\\Providers\\AppServiceProvider), 63)
#22 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1041): array_walk(Array, Object(Closure))
#23 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#24 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(286): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#25 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(447): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#26 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(199): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#27 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#28 {main}

[previous exception] [object] (PDOException(code: 1045): SQLSTATE[HY000] [1045] Access denied for user 'forge'@'localhost' (using password: YES) at D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php:65)
[stacktrace]
#0 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(65): PDO->__construct('mysql:host=127....', 'forge', '', Array)
#1 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(45): Illuminate\\Database\\Connectors\\Connector->createPdoConnection('mysql:host=127....', 'forge', '', Array)
#2 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\MySqlConnector.php(24): Illuminate\\Database\\Connectors\\Connector->createConnection('mysql:host=127....', Array, Array)
#3 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(184): Illuminate\\Database\\Connectors\\MySqlConnector->connect(Array)
#4 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->Illuminate\\Database\\Connectors\\{closure}()
#5 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1339): call_user_func(Object(Closure))
#6 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1375): Illuminate\\Database\\Connection->getPdo()
#7 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(528): Illuminate\\Database\\Connection->getReadPdo()
#8 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(423): Illuminate\\Database\\Connection->getPdoForSelect(true)
#9 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(816): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select `name`, ...', Array)
#10 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('select `name`, ...', Array, Object(Closure))
#11 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(431): Illuminate\\Database\\Connection->run('select `name`, ...', Array, Object(Closure))
#12 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2914): Illuminate\\Database\\Connection->select('select `name`, ...', Array, true)
#13 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2902): Illuminate\\Database\\Query\\Builder->runSelect()
#14 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3456): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#15 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2903): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#16 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(739): Illuminate\\Database\\Query\\Builder->get(Array)
#17 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(723): Illuminate\\Database\\Eloquent\\Builder->getModels(Array)
#18 D:\\Workstation\\hoomi\\vendor\\spatie\\laravel-settings\\src\\SettingsRepositories\\DatabaseSettingsRepository.php(28): Illuminate\\Database\\Eloquent\\Builder->get(Array)
#19 D:\\Workstation\\hoomi\\vendor\\spatie\\laravel-settings\\src\\SettingsMapper.php(86): Spatie\\LaravelSettings\\SettingsRepositories\\DatabaseSettingsRepository->getPropertiesInGroup('developer')
#20 D:\\Workstation\\hoomi\\vendor\\spatie\\laravel-settings\\src\\SettingsMapper.php(37): Spatie\\LaravelSettings\\SettingsMapper->fetchProperties('App\\\\Settings\\\\De...', Object(Illuminate\\Support\\Collection))
#21 D:\\Workstation\\hoomi\\vendor\\spatie\\laravel-settings\\src\\Settings.php(277): Spatie\\LaravelSettings\\SettingsMapper->load('App\\\\Settings\\\\De...')
#22 D:\\Workstation\\hoomi\\vendor\\spatie\\laravel-settings\\src\\Settings.php(101): Spatie\\LaravelSettings\\Settings->loadValues()
#23 D:\\Workstation\\hoomi\\app\\Providers\\AppServiceProvider.php(60): Spatie\\LaravelSettings\\Settings->__get('debug_mode')
#24 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Tasawk\\Providers\\AppServiceProvider->boot()
#25 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#26 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#27 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#28 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#29 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1059): Illuminate\\Container\\Container->call(Array)
#30 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1040): Illuminate\\Foundation\\Application->bootProvider(Object(Tasawk\\Providers\\AppServiceProvider))
#31 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(Tasawk\\Providers\\AppServiceProvider), 63)
#32 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1041): array_walk(Array, Object(Closure))
#33 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#34 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(286): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#35 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(447): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#36 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(199): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#37 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#38 {main}
"} 
[2025-05-28 09:32:12] production.ERROR: SQLSTATE[HY000] [1045] Access denied for user 'forge'@'localhost' (using password: YES) (Connection: mysql, SQL: select `name`, `payload` from `settings` where `group` = developer) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 1045): SQLSTATE[HY000] [1045] Access denied for user 'forge'@'localhost' (using password: YES) (Connection: mysql, SQL: select `name`, `payload` from `settings` where `group` = developer) at D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:829)
[stacktrace]
#0 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('select `name`, ...', Array, Object(Closure))
#1 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(431): Illuminate\\Database\\Connection->run('select `name`, ...', Array, Object(Closure))
#2 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2914): Illuminate\\Database\\Connection->select('select `name`, ...', Array, true)
#3 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2902): Illuminate\\Database\\Query\\Builder->runSelect()
#4 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3456): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#5 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2903): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#6 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(739): Illuminate\\Database\\Query\\Builder->get(Array)
#7 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(723): Illuminate\\Database\\Eloquent\\Builder->getModels(Array)
#8 D:\\Workstation\\hoomi\\vendor\\spatie\\laravel-settings\\src\\SettingsRepositories\\DatabaseSettingsRepository.php(28): Illuminate\\Database\\Eloquent\\Builder->get(Array)
#9 D:\\Workstation\\hoomi\\vendor\\spatie\\laravel-settings\\src\\SettingsMapper.php(86): Spatie\\LaravelSettings\\SettingsRepositories\\DatabaseSettingsRepository->getPropertiesInGroup('developer')
#10 D:\\Workstation\\hoomi\\vendor\\spatie\\laravel-settings\\src\\SettingsMapper.php(37): Spatie\\LaravelSettings\\SettingsMapper->fetchProperties('App\\\\Settings\\\\De...', Object(Illuminate\\Support\\Collection))
#11 D:\\Workstation\\hoomi\\vendor\\spatie\\laravel-settings\\src\\Settings.php(277): Spatie\\LaravelSettings\\SettingsMapper->load('App\\\\Settings\\\\De...')
#12 D:\\Workstation\\hoomi\\vendor\\spatie\\laravel-settings\\src\\Settings.php(101): Spatie\\LaravelSettings\\Settings->loadValues()
#13 D:\\Workstation\\hoomi\\app\\Providers\\AppServiceProvider.php(60): Spatie\\LaravelSettings\\Settings->__get('debug_mode')
#14 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Tasawk\\Providers\\AppServiceProvider->boot()
#15 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#16 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#17 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#18 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#19 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1059): Illuminate\\Container\\Container->call(Array)
#20 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1040): Illuminate\\Foundation\\Application->bootProvider(Object(Tasawk\\Providers\\AppServiceProvider))
#21 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(Tasawk\\Providers\\AppServiceProvider), 63)
#22 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1041): array_walk(Array, Object(Closure))
#23 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#24 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(286): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#25 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(447): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#26 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(199): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#27 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#28 {main}

[previous exception] [object] (PDOException(code: 1045): SQLSTATE[HY000] [1045] Access denied for user 'forge'@'localhost' (using password: YES) at D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php:65)
[stacktrace]
#0 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(65): PDO->__construct('mysql:host=127....', 'forge', '', Array)
#1 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(45): Illuminate\\Database\\Connectors\\Connector->createPdoConnection('mysql:host=127....', 'forge', '', Array)
#2 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\MySqlConnector.php(24): Illuminate\\Database\\Connectors\\Connector->createConnection('mysql:host=127....', Array, Array)
#3 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(184): Illuminate\\Database\\Connectors\\MySqlConnector->connect(Array)
#4 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->Illuminate\\Database\\Connectors\\{closure}()
#5 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1339): call_user_func(Object(Closure))
#6 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1375): Illuminate\\Database\\Connection->getPdo()
#7 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(528): Illuminate\\Database\\Connection->getReadPdo()
#8 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(423): Illuminate\\Database\\Connection->getPdoForSelect(true)
#9 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(816): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select `name`, ...', Array)
#10 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('select `name`, ...', Array, Object(Closure))
#11 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(431): Illuminate\\Database\\Connection->run('select `name`, ...', Array, Object(Closure))
#12 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2914): Illuminate\\Database\\Connection->select('select `name`, ...', Array, true)
#13 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2902): Illuminate\\Database\\Query\\Builder->runSelect()
#14 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3456): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#15 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2903): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#16 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(739): Illuminate\\Database\\Query\\Builder->get(Array)
#17 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(723): Illuminate\\Database\\Eloquent\\Builder->getModels(Array)
#18 D:\\Workstation\\hoomi\\vendor\\spatie\\laravel-settings\\src\\SettingsRepositories\\DatabaseSettingsRepository.php(28): Illuminate\\Database\\Eloquent\\Builder->get(Array)
#19 D:\\Workstation\\hoomi\\vendor\\spatie\\laravel-settings\\src\\SettingsMapper.php(86): Spatie\\LaravelSettings\\SettingsRepositories\\DatabaseSettingsRepository->getPropertiesInGroup('developer')
#20 D:\\Workstation\\hoomi\\vendor\\spatie\\laravel-settings\\src\\SettingsMapper.php(37): Spatie\\LaravelSettings\\SettingsMapper->fetchProperties('App\\\\Settings\\\\De...', Object(Illuminate\\Support\\Collection))
#21 D:\\Workstation\\hoomi\\vendor\\spatie\\laravel-settings\\src\\Settings.php(277): Spatie\\LaravelSettings\\SettingsMapper->load('App\\\\Settings\\\\De...')
#22 D:\\Workstation\\hoomi\\vendor\\spatie\\laravel-settings\\src\\Settings.php(101): Spatie\\LaravelSettings\\Settings->loadValues()
#23 D:\\Workstation\\hoomi\\app\\Providers\\AppServiceProvider.php(60): Spatie\\LaravelSettings\\Settings->__get('debug_mode')
#24 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Tasawk\\Providers\\AppServiceProvider->boot()
#25 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#26 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#27 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#28 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#29 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1059): Illuminate\\Container\\Container->call(Array)
#30 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1040): Illuminate\\Foundation\\Application->bootProvider(Object(Tasawk\\Providers\\AppServiceProvider))
#31 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(Tasawk\\Providers\\AppServiceProvider), 63)
#32 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1041): array_walk(Array, Object(Closure))
#33 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#34 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(286): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#35 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(447): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#36 D:\\Workstation\\hoomi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(199): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#37 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#38 {main}
"} 
