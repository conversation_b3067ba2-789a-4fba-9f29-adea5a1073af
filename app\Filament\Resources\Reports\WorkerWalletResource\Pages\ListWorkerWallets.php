<?php

namespace Tasawk\Filament\Resources\Reports\WorkerWalletResource\Pages;


use Filament\Actions\Action;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Tabs;
use Filament\Forms\Components\TextInput;
use Filament\Resources\Pages\ListRecords;
use Tasawk\Filament\Resources\Reports\WorkerWalletResource;

class ListWorkerWallets extends ListRecords {
    protected static string $resource = WorkerWalletResource::class;

    protected function getHeaderActions(): array {
        return [

        ];
    }
}
