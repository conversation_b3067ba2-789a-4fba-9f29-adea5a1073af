<?php

namespace Tasawk\Http\Requests\Api\Manager;

use Illuminate\Foundation\Http\FormRequest;
use Str;

class PricingOrderRequest extends FormRequest {

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize() {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules() {
        return [
//            'services_cost' => ['required', 'numeric', 'min:1'],
            'items'=>['required','array'],
            'items.*.name'=>['required','string','min:2'],
            'items.*.price'=>['required','numeric','min:2'],
            'items.*.quantity'=>['required','numeric','min:1'],
            'items.*.image'=>['nullable','image'],
        ];
    }


}
