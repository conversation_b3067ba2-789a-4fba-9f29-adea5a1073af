<?php

namespace Tasawk\Filament\Resources\Reports;

use Filament\Forms\Components\DatePicker;
use Filament\Infolists\Components\TextEntry;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Filters\Filter;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;
use Money\Money;
use pxlrbt\FilamentExcel\Actions\Tables\ExportBulkAction;
use pxlrbt\FilamentExcel\Exports\ExcelExport;
use Tasawk\Filament\Resources\OrderResource;
use Tasawk\Filament\Resources\Reports\ProfitsResource\Pages;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;

use Tasawk\Models\Order;
use Tasawk\Traits\Filament\HasTranslationLabel;

class ProfitsResource extends Resource {
    use HasTranslationLabel;

    protected static ?string $model = Order::class;

    protected static ?string $navigationIcon = 'heroicon-o-currency-dollar';

    public static function form(Form $form): Form {
        return $form
            ->schema([
                //
            ]);
    }

    public static function table(Table $table): Table {
        return $table
            ->modifyQueryUsing(fn(Builder $query) => $query
                ->paid()
                ->fromSub(
                    "
            SELECT *,
                   total / (1 +
                            ( SELECT value FROM orders_conditions WHERE order_id = orders.id AND TYPE = 'taxes') / 100)
                       AS totalWithoutVat
       FROM orders",
                    'orders'
                )
                ->select(DB::raw('*,
                total -totalWithoutVat AS taxes,
            (totalWithoutVat - ((totalWithoutVat/100)*app_commission_percentage)) as worker_dues,
            (totalWithoutVat - ((totalWithoutVat/100)*(100-app_commission_percentage))) as app_commission'))
            )
            ->columns([

                TextColumn::make('order_number')->searchable(['id']),
                TextColumn::make('date')->date()->searchable(),
                TextColumn::make('total')->label(__('forms.fields.totals'))->formatStateUsing(fn($record) => $record->total->format()),
                TextColumn::make('totalWithoutVat')->default(0)
                    ->label(__('forms.fields.total_without_vat'))
                    ->formatStateUsing(fn($state) => \Cknow\Money\Money::parse($state)->format()),

                TextColumn::make('taxes')
                    ->label(__('forms.fields.taxes'))
                    ->formatStateUsing(fn($state) => \Cknow\Money\Money::parse($state)->format()),
//                    ->state(fn($record) => \Cknow\Money\Money::parse($record->as_cart->totals()['taxes'])->format()),
                TextColumn::make('app_commission')
                    ->formatStateUsing(fn($state) => \Cknow\Money\Money::parse($state)->format())
                    ->summarize(Tables\Columns\Summarizers\Sum::make()->money('SAR')),
                TextColumn::make('worker_dues')
                    ->formatStateUsing(fn($state) => \Cknow\Money\Money::parse($state)->format())
                    ->summarize(Tables\Columns\Summarizers\Sum::make()->money('SAR')),

            ])
            ->filters([
                Filter::make('created_at')
                    ->form([
                        DatePicker::make('date_from'),
                        DatePicker::make('date_until'),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['date_from'] ?? '',
                                fn(Builder $query, $date): Builder => $query->whereDate('date', '>=', $date),
                            )
                            ->when(
                                $data['date_until'] ?? '',
                                fn(Builder $query, $date): Builder => $query->whereDate('date', '<=', $date),
                            );
                    })
            ])
            ->actions([
            ])
            ->bulkActions([

                Tables\Actions\BulkActionGroup::make([
                    ExportBulkAction::make()->exports([
                        ExcelExport::make("CSV")
                            ->fromTable()
                            ->withFilename(fn() => static::getPluralLabel() . '-' . now()->format('Y-m-d'))
                            ->withWriterType(\Maatwebsite\Excel\Excel::XLSX),


                    ]),
                ]),
            ]);
    }

    public static function getRelations(): array {
        return [
            //
        ];
    }

    public static function getPages(): array {
        return [
            'index' => Pages\ListProfits::route('/'),
        ];
    }

    public static function getPluralLabel(): ?string {
        return __('menu.profits');
    }

    public static function getNavigationGroup(): ?string {
        return __('menu.reports');
    }

}
