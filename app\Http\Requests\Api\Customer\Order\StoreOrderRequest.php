<?php

namespace Tasawk\Http\Requests\Api\Customer\Order;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;
use Tasawk\Enum\AccountTypeEnum;
use Tasawk\Enum\OrderTypeEnum;
use Tasawk\Lib\Utils;
use Tasawk\Models\Category;
use Tasawk\Rules\AddressBelongToAuthUserRule;
use Tasawk\Rules\IsAddressLocationInsideBranchBoundaries;
use Tasawk\Rules\IsDayDateInWorkingDateRule;
use Tasawk\Rules\IsProductAvailableInBranchRule;
use Tasawk\Rules\IsRequiredProductOptionsRepresentRule;
use Tasawk\Rules\IsValidProductOptionsRule;
use Tasawk\Rules\IsValidProductOptionValuesRule;
use Tasawk\Settings\GeneralSettings;


class StoreOrderRequest extends FormRequest {

    public function authorize() {
        return true;
    }


    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules(): array {
        return [
            "category_id" => ['required', 'exists:categories,id'],
            'sub_category_id' => ['nullable', 'exists:categories,id'],

            // Support for multiple services with quantities
            'services' => ['required', 'array', 'min:1', 'max:10'],
            'services.*.service_id' => ['required', 'exists:services,id'],
            'services.*.quantity' => ['required', 'integer', 'min:1', 'max:50'],

            // Keep backward compatibility with single service
            'service_id' => ['nullable', 'exists:services,id'],

            'worker_type' => ['required',Rule::enum(AccountTypeEnum::class)],
            "address_id" => [Rule::requiredIf($this->get('receipt_method') == 'delivery'), new AddressBelongToAuthUserRule],
            'order_type' => ['required', Rule::enum(OrderTypeEnum::class)],
            'date' => [Rule::requiredIf($this->get('order_type') == OrderTypeEnum::NORMAL->value), 'date', 'date_format:Y-m-d'],
            'time' => [Rule::requiredIf($this->get('order_type') == OrderTypeEnum::NORMAL->value), 'date_format:H:i'],
            'notes' => ['nullable', 'min:3'],
            'images' => ['nullable', 'array'],
            'images.*' => ['required', 'image'],

        ];
    }


}
