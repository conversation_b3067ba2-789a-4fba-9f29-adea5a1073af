<?php

namespace Tasawk\Api\V1\Manager;

use Illuminate\Http\Request;
use MatanYadaev\EloquentSpatial\Objects\Point;
use Tasawk\Actions\Manager\UpdateManagerProfileAction;
use Tasawk\Actions\Shared\Authentication\UpdateUserPassword;
use Tasawk\Api\Core;
use Tasawk\Api\Facade\Api;
use Tasawk\Enum\OrderStatus;
use Tasawk\Http\Requests\Api\Customer\Profile\ProfileSettingRequest;
use Tasawk\Http\Requests\Api\Manager\Profile\UpdateLocationRequest;
use Tasawk\Http\Requests\Api\Manager\Profile\UpdateManagerPasswordRequest;
use Tasawk\Http\Requests\Api\Manager\Profile\UpdateManagerProfileRequest;
use Tasawk\Http\Requests\Api\Manager\Profile\UpdateProfileRequest;
use Tasawk\Http\Resources\Api\Manager\ManagerResource;
use Tasawk\Http\Resources\Api\Manager\StatisticsResource;
use Tasawk\Http\Resources\Api\Manager\WorkerResource;
use Tasawk\Models\Worker;


class ProfileServices {
    public function index() {
        return Api::isOk(__("Manager information"))->setData(new WorkerResource(auth()->user()));
    }

    public function update(UpdateProfileRequest $request) {
        UpdateManagerProfileAction::run($request);

        auth()->user()->refresh();
        return Api::isOk(__("Account information updated"))->setData(new WorkerResource(auth()->user()));
    }

    public function updateLocation(UpdateLocationRequest $request) {
        $location = $request->validated();;
        auth()->user()->fleetAccount()->update(['location' => (new Point($location['lat'], $location['lng']))->toSqlExpression(auth()->user()->getConnection())]);
        return Api::isOk("updated");

    }

    public function updatePassword(UpdateManagerPasswordRequest $request): Core {
        UpdateUserPassword::run(auth()->user(), $request->get('password'));
        return Api::isOk(__("Account information updated"))->setData(new WorkerResource(auth()->user()));
    }

    public function settings(ProfileSettingRequest $request) {
        auth()->user()->update(['settings' => $request->validated()]);
        return Api::isOk(__("User settings updated"));
    }

    public function toggleAvailability() {
        $fleetAccount = auth()->user()->fleetAccount;
        $fleetAccount->update(['active' => !$fleetAccount->active]);
        return Api::isOk(__("done"));
    }

    public function statistics() {
        return Api::isOk('statistics', StatisticsResource::collection(auth()->user()->actAs(Worker::class)->orders()->whereStatus(OrderStatus::COMPLETED)->latest()->paginate()));
    }

}
