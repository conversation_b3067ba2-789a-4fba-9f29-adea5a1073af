<?php

namespace Tasawk\Actions;

use Illuminate\Support\Facades\DB;
use Lorisleiva\Actions\Concerns\AsAction;
use MyFatoorah\Library\API\Payment\MyFatoorahPaymentStatus;
use MyFatoorah\Library\MyfatoorahApiV2;
use MyFatoorah\Library\PaymentMyfatoorahApiV2;
use Tasawk\Actions\Manager\DepositWorkerDuesAfterOrderCompletedAction;
use Tasawk\Enum\OrderStatus;
use Tasawk\Models\Order;
use Theamostafa\Wallet\Models\Transaction;

class MyFatoorahCallBackAction {
    use AsAction;

    public function handle() {
        $response = app(MyFatoorahPaymentStatus::class)->getPaymentStatus(request()->get('Id'), 'PaymentId');
        $userDefinedField = json_decode($response->UserDefinedField, true);

        if ($response->InvoiceStatus != 'Paid') {
            return;
        }

        if (isset($userDefinedField['type']) && $userDefinedField['type']) {

            $this->addBalanceToWallet($response->CustomerReference);
            return;
        }
        $this->markOrderAsPaid($response->InvoiceId, $response);
    }

    public function addBalanceToWallet($transaction_id): void {
        $transaction = Transaction::where("id", $transaction_id)->first();
        $transaction->update(['confirmed' => true]);
        $transaction->wallet->update([
            'balance' => $transaction->wallet->transactions()->select(
                DB::raw(
                    "CASE WHEN type = 'deposit' THEN amount ELSE CAST(-amount AS float) END as balance"
                ))
                ->where('confirmed', true)
                ->get()
                ->sum('balance')
        ]);;
    }

    public function markOrderAsPaid($invoice_id, $response): void {
        $order = Order::where('payment_data->invoiceId', $invoice_id)->first();
        if ($order->payment_status != 'paid') {
            $order->update([
                'status' => OrderStatus::COMPLETED,
                'payment_data' => array_merge($order->payment_data, [...collect($response)->toArray(), 'method' => $response->focusTransaction->PaymentGateway, 'paid_at' => now()]),
                'payment_status' => 'paid',

            ]);
            DepositWorkerDuesAfterOrderCompletedAction::run($order);
        }


    }
}
