<?php

namespace Tasawk\Api\V1\Manager;


use Api;
use Auth;
use Tasawk\Actions\Manager\ManagerHasRightsToLogin;
use Tasawk\Actions\Manager\RegisterWorkerAccountAction;
use Tasawk\Actions\Manager\WorkerHasRightsToLogin;
use Tasawk\Actions\Shared\Authentication\ForgetPassword;
use Tasawk\Actions\Shared\Authentication\SendVerificationCode;
use Tasawk\Actions\Shared\Authentication\UpdateUserPassword;
use Tasawk\Actions\Shared\Authentication\UpdateUserToken;
use Tasawk\Actions\Shared\Authentication\VerifyUserAccount;
use Tasawk\Api\Core;
use Tasawk\Http\Requests\Api\Customer\Auth\VerifyAccountRequest;
use Tasawk\Http\Requests\Api\Manager\Authentication\CodeConfirmRequest;
use Tasawk\Http\Requests\Api\Manager\Authentication\ForgetPasswordRequest;
use Tasawk\Http\Requests\Api\Manager\Authentication\LoginRequest;
use Tasawk\Http\Requests\Api\Manager\Authentication\RegisterRequest;
use Tasawk\Http\Requests\Api\Manager\Authentication\ResetPasswordRequest;
use Tasawk\Http\Resources\Api\Customer\CustomerResource;
use Tasawk\Http\Resources\Api\Manager\ManagerResource;
use Tasawk\Http\Resources\Api\Manager\WorkerResource;


class AuthServices {

    public function login(LoginRequest $request) {
        if (!Auth::once($request->only("phone", 'password'))) {
            return Api::isError(__('validation.api.invalid_credentials'))->setErrors(['credentials' => __('validation.api.invalid_credentials')]);
        }
        WorkerHasRightsToLogin::run();
        UpdateUserToken::run(auth()->user());

        return Api::isOk(__("Manager information"))->setData(new WorkerResource(auth()->user()));
    }

    public function register(RegisterRequest $request) {
        $worker=RegisterWorkerAccountAction::run($request->validated());
        SendVerificationCode::run($worker);
        return Api::isOk(__("SMS code sent"));


}
    public function verify(VerifyAccountRequest $request) {
        VerifyUserAccount::run($request->currentUser());
        return Api::isOk(__("Verified,User information"))->setData(new WorkerResource($request->currentUser()));

    }
    public function verifySMSCode(CodeConfirmRequest $request): Core {
        return Api::isOk(__("Correct Verification code"));

    }

    public function forgetPassword(ForgetPasswordRequest $request): Core {
        ForgetPassword::run($request->currentUser());
        return Api::isOk(__("SMS code sent"));

    }

    public function resetPassword(ResetPasswordRequest $request): Core {
        UpdateUserPassword::run($request->currentUser(), $request->get('password'));
        return Api::isOk(__("User information"))->setData(new ManagerResource($request->currentUser()));

    }



}
