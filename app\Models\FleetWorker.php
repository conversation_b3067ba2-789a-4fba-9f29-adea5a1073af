<?php

namespace Tasawk\Models;

use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Matan<PERSON>adaev\EloquentSpatial\Objects\Point;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;
use Tasawk\Enum\AccountTypeEnum;
use Tasawk\Enum\NationalityEnum;
use Tasawk\Enum\ResidenceTypeEnum;

class FleetWorker extends Model implements HasMedia {
    use HasFactory, InteractsWithMedia;

    protected $guarded = ['id'];
    protected $casts = [
        'location' => Point::class,
        'account_type' => AccountTypeEnum::class,
        'nationality' => NationalityEnum::class,
        'residence_type' => ResidenceTypeEnum::class
    ];

    protected function location(): Attribute {
        return Attribute::make(
            set: function ($coordinate) {
                $this->refresh();
                return (new Point($coordinate['lat'], $coordinate['lng']))->toSqlExpression($this->getConnection());
            }
        );
    }

    public function categories(): BelongsToMany {
        return $this->belongsToMany(Category::class, 'fleet_worker_categories', 'fleet_worker_id', 'category_id');
    }

    public function user() {
        return $this->belongsTo(User::class);
    }
}
