<?php

namespace Tasawk\Http\Resources\Api\Manager\Orders;

use Illuminate\Http\Resources\Json\JsonResource;
use Tasawk\Brands\Resources\Api\BrandResources;
use Tasawk\Fleet\Http\Resources\DriverResource;
use Tasawk\Jobs\Http\Resources\Api\RateResources;
use Tasawk\Orders\Resources\Api\AddressOrderResource;
use Tasawk\Orders\Resources\Api\UserOrderResource;

class RateResource extends JsonResource {

    public function toArray($request) {
        return [
            "id"=>$this->id,
            'customer'=>CustomerResource::make($this->order->customer),
            'order_id'=>$this->order_id,
            "rate" =>(float)$this->rate,
            "comment" => $this->comment,
            'created_at'=>$this->created_at->format("Y-m-d h:i a")
        ];
    }
}
