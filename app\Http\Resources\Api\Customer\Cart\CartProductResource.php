<?php

namespace Tasawk\Http\Resources\Api\Customer\Cart;

use Arr;
use Cknow\Money\Money;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;


class CartProductResource extends JsonResource {

    /**
     * Transform the resource into an array.
     *
     * @param Request $request
     * @return array
     */
    public function toArray($request) {

        return [
            'id' => $this->id,
            "name" => $this->name,
            "quantity" => $this->quantity,
            $this->mergeWhen(data_get($this->associatedModel, 'image'), [
                'image' => data_get($this->associatedModel, 'image'),
            ]),
            'price' => Money::parse($this->price)->format(),
            'price_sum' => Money::parse($this->getPriceSumWithConditions())->format()
        ];
    }

}
