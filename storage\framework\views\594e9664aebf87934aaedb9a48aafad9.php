<div aria-live="polite" aria-atomic="true"
     class="m-2 "
     style="position: fixed; bottom: 0;z-index: 999999999; min-width: 300px"

>
    <div class="toast">
        <div class="toast-header">
            <img class="logo-dark" src="<?php echo e(asset('storage/' .$logo_dark)); ?>" alt="logo">
                        <strong class="mr-auto"></strong>
            
            <button type="button" class="ml-2 mb-1 close" data-dismiss="toast" aria-label="Close">
                <span aria-hidden="true">&times;</span>
            </button>
        </div>
        <div class="toast-body" id="flashMessageText"></div>
    </div>
</div>
<script>

    document.addEventListener('livewire:init', () => {
        Livewire.on('flash', (event) => {
            $('#flashMessageText').html(event[0])
            $('.toast').toast({
                delay: 3000
            }).toast('show');
        });
    });
</script>
<?php /**PATH D:\Workstation\hoomi\resources\views/site/includes/flash-message.blade.php ENDPATH**/ ?>