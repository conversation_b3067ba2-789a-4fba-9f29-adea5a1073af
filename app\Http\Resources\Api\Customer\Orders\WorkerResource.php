<?php

namespace Tasawk\Http\Resources\Api\Customer\Orders;

use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Str;
use Tasawk\Actions\GetDistanceBetweenLocationsAction;
use Tasawk\Enum\OrderStatus;
use Tasawk\Http\Resources\Api\Customer\AddressBookResource;
use Tasawk\Http\Resources\Api\Customer\Branches\BranchResource;
use Tasawk\Http\Resources\Api\Customer\Branches\LightBranchResource;
use Tasawk\Http\Resources\Api\Customer\Cart\CartProductResource;
use Tasawk\Http\Resources\Api\Customer\RateResource;
use Tasawk\Http\Resources\Api\Customer\ReportResource;
use Tasawk\Http\Resources\Api\Shared\CategoryResource;
use Tasawk\Http\Resources\Api\Shared\WorkerTypeResource;
use Tasawk\Settings\GeneralSettings;

class WorkerResource extends JsonResource {

    /**
     * Transform the resource into an array.
     *
     * @param Request $request
     * @return array
     */
    public function toArray($request) {
        $address= $request->get('address');

        $distance = GetDistanceBetweenLocationsAction::run(
            ['lat' => $address->map_location['lat'] ?? '', 'lng' => $address->map_location['lng']],
            ['lat' => $this->fleetAccount?->location?->getCoordinates()[1], 'lng' => $this->fleetAccount?->location?->getCoordinates()[0]],
        );
        return [
            "id" => $this->id,
            'logo'=>$this->fleetAccount?->getFirstMediaUrl('logo'),
            'name' => $this->name,
            'phone'=>$this->phone,
            'distance'=>$distance,
            'rating' =>(float) $this->rating->avg('rate'),


        ];
    }
}
