<?php

namespace Tasawk\Filament\Pages\Settings;

use <PERSON><PERSON>hanSalleh\FilamentShield\Traits\HasPageShield;
use Filament\Forms;
use Filament\Forms\Components\FileUpload;
use Filament\Forms\Components\Repeater;
use Filament\Forms\Components\Tabs;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Pages\SettingsPage;
use Illuminate\Contracts\Support\Htmlable;
use Tasawk\Forms\Components\SelectFontAwesomeIcon;
use Tasawk\Settings\LandingPageSettings;

class ManageLanding extends SettingsPage
{
    use HasPageShield;

    protected static ?string $navigationIcon = 'heroicon-o-rocket-launch';

    protected static ?string $slug = 'settings/landing';

    protected static string $settings = LandingPageSettings::class;

    public function form(Form $form): Form
    {
        return $form
            ->schema([

                // Forms\Components\Section::make("basic_information")
                // ->label(__("sections.basic_information"))
                // ->schema([
                FileUpload::make('logo_light')
                    ->label(__('forms.fields.logo_light'))
                    ->required(),
                FileUpload::make('logo_dark')
                    ->label(__('forms.fields.logo_dark'))
                    ->columns(2)
                    ->required(),
                // ]),
                Forms\Components\Section::make('about_us')

                    ->label(__('sections.about_us'))
                    ->schema([
                        Tabs::make('Label')
                            ->tabs([
                                Tabs\Tab::make(__('panel.languages.arabic'))
                                    ->schema([
                                        TextArea::make('about_description.ar')
                                            ->label(__('forms.fields.description'))
                                            ->required(),

                                    ]),
                                Tabs\Tab::make(__('panel.languages.english'))
                                    ->schema([
                                        Textarea::make('about_description.en')
                                            ->label(__('forms.fields.description'))
                                            ->required(),
                                    ]),
                            ]),
                        Repeater::make('about_features')
                            ->label(__('sections.about_features'))
                            ->schema([
                                Tabs::make('Label')
                                    ->tabs([
                                        Tabs\Tab::make(__('panel.languages.arabic'))
                                            ->schema([
                                                TextInput::make('title.ar')
                                                    ->label(__('forms.fields.title'))
                                                    ->required(),

                                            ]),
                                        Tabs\Tab::make(__('panel.languages.english'))
                                            ->schema([
                                                TextInput::make('title.en')
                                                    ->label(__('forms.fields.title'))
                                                    ->required(),

                                            ]),
                                    ]),
                                SelectFontAwesomeIcon::make('icon')
                                    ->setMode('all')
                                    ->searchable()
                                    ->required()
                                    ->allowHtml(),
                            ])->defaultItems(1),
                    ]),
                Forms\Components\Section::make('our_features')
                    ->label(__('sections.our_features'))
                    ->schema([
                        Tabs::make('Label')
                            ->tabs([
                                Tabs\Tab::make(__('panel.languages.arabic'))
                                    ->schema([
                                        Textarea::make('our_features_description.ar')
                                            ->label(__('forms.fields.description'))
                                            ->required(),
                                    ]),
                                Tabs\Tab::make(__('panel.languages.english'))
                                    ->schema([
                                        Textarea::make('our_features_description.en')
                                            ->label(__('forms.fields.description'))
                                            ->required(),
                                    ]),
                            ]),
                        Repeater::make('features')
                            ->label(__('sections.about_features'))
                            ->schema([
                                Tabs::make('Label')
                                    ->tabs([
                                        Tabs\Tab::make(__('panel.languages.arabic'))
                                            ->schema([
                                                TextInput::make('title.ar')
                                                    ->label(__('forms.fields.title'))
                                                    ->required(),
                                                SelectFontAwesomeIcon::make('icon')
                                                    ->setMode('all')
                                                    ->searchable()
                                                    ->required()
                                                    ->allowHtml(),
                                            ]),
                                        Tabs\Tab::make(__('panel.languages.english'))
                                            ->schema([
                                                TextInput::make('title.en')
                                                    ->label(__('forms.fields.title'))
                                                    ->required(),
                                                SelectFontAwesomeIcon::make('icon')
                                                    ->setMode('all')
                                                    ->searchable()
                                                    ->required()
                                                    ->allowHtml(),

                                            ]),
                                    ]),

                            ])->defaultItems(1),
                        FileUpload::make('feature_image')
                            ->label(__('forms.fields.image'))
                            ->required(),
                    ]),
                Forms\Components\Section::make('app_screen')
                    ->label(__('sections.app_screen'))
                    ->schema([

                        Repeater::make('app_screen')
                            ->label(__('sections.app_screen'))
                            ->schema([
                                FileUpload::make('image')
                                    ->label(__('forms.fields.image'))
                                    ->required(),
                            ])->defaultItems(1),

                    ]),

            ])->columns(2);
    }

    public static function getNavigationLabel(): string
    {
        return __('menu.landing_page');
    }

    public function getHeading(): string|Htmlable
    {
        return __('sections.landing_page');
    }

    public static function getNavigationGroup(): ?string
    {
        return __('menu.settings');
    }

    public function getTitle(): string|Htmlable
    {
        return __('sections.landing_page');
    }

    public function getBreadcrumbs(): array
    {
        return [
            null => static::getNavigationGroup(),
            static::getUrl() => static::getNavigationLabel(),
        ];
    }
}