<script src="https://cdnjs.cloudflare.com/ajax/libs/ion-sound/3.0.7/js/ion.sound.min.js"
        integrity="sha512-k0RyhyJoNdQfdrx7Yb5+zbrtFp8CVsGMJPlQkcNsNZi82GS0R09TG1F/Ar1LuUSXrkVMuk7SftnrXK35nAfdYg=="
        crossorigin="anonymous" referrerpolicy="no-referrer"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/axios/1.5.1/axios.min.js"
        integrity="sha512-emSwuKiMyYedRwflbZB2ghzX8Cw8fmNVgZ6yQNNXXagFzFOaQmbvQ1vmDkddHjm5AITcBIZfC7k4ShQSjgPAmQ=="
        crossorigin="anonymous" referrerpolicy="no-referrer"></script>
<script src="https://www.gstatic.com/firebasejs/8.3.2/firebase-app.js"></script>
<script src="https://www.gstatic.com/firebasejs/8.3.2/firebase-messaging.js"></script>
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css"
      integrity="sha512-z3gLpd7yknf1YoNbCzqRKc4qyor8gaKU1qmn+CShxbuBusANI9QpRohGBreCFkKxLhei6S9CQXFEbbKuqLg0DA=="
      crossorigin="anonymous" referrerpolicy="no-referrer"/>
<script>
    ion.sound({
        sounds: [
            {name: "bell_ring"},
        ],

        // main config
        path: "/sounds/",
        preload: true,
        multiplay: true,
        volume: 0.9
    });

</script>
<script>
    const firebaseConfig = {
        apiKey: "AIzaSyDru5MD-xthApoi-El-UrgZrczlni4k5Lk",
        authDomain: "kufa-1f697.firebaseapp.com",
        projectId: "kufa-1f697",
        storageBucket: "kufa-1f697.appspot.com",
        messagingSenderId: "154097159854",
        appId: "1:154097159854:web:6c01d52108d279acb8673f",
        measurementId: "G-HK2YYHX4J3"
    };

    firebase.initializeApp(firebaseConfig);

    const messaging = firebase.messaging();

    function initFirebaseMessagingRegistration() {
        messaging.requestPermission().then(function () {
            return messaging.getToken()
        }).then(function (token) {
            axios.post(route('admin.fcm-token', {'device_token': token}), {
                _method: "put",
                token,
                headers: {'X-CSRF-TOKEN': '<?php echo e(csrf_token()); ?>'}
            })

        }).catch(function (err) {
            console.log(`Token Error :: ${err}`);
        });
    }

    initFirebaseMessagingRegistration();
    messaging.onMessage(function (payload) {
        // new Notification(payload.notification.title, {body: payload.notification.body});

        new FilamentNotification()
            .title(payload.notification.title)
            .body(payload.notification.body)
            .icon('heroicon-o-shopping-bag')
            .actions([
                new FilamentNotificationAction("<?php echo e(__('sections.view')); ?>")
                    .button()
                    .url(urlMapper(payload.data.entity_type, payload.data.entity_id))
                    .openUrlInNewTab(),
            ])
            .send()
        ion.sound.play("bell_ring");

    });

    const urlMapper = function (entityType, entityID) {
        const mapping = {
            order: route('filament.admin.resources.orders.view', entityID),
            branch: route('filament.admin.resources.catalog.branches.edit', entityID)
        }
        return mapping[entityType];
    };
</script>
<?php /**PATH D:\Workstation\hoomi\resources\views/filament/firebase-initialization.blade.php ENDPATH**/ ?>