<?php

namespace Tasawk\Filament\Resources\Crm;

use Filament\Tables\Filters\Filter;
use Filament\Tables\Filters\TernaryFilter;
use Tasawk\Filament\Resources\Crm\AddressBookResource\Pages;
use Tasawk\Models\AddressBook;
use Tasawk\Models\Location\City;
use Tasawk\Models\Location\District;
use Tasawk\Models\Zone;
use Tasawk\Traits\Filament\HasTranslationLabel;
use Cheesegrits\FilamentGoogleMaps\Fields\Map;
use Filament\Forms;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Columns\IconColumn;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;

class AddressBookResource extends Resource {

    protected static ?string $model = AddressBook::class;
    protected static ?string $navigationIcon = 'heroicon-o-identification';
    protected static ?int $navigationSort = 2;

    public static function form(Form $form): Form {
        return $form
            ->schema([
                TextInput::make('name')
                    ->label(__("forms.fields.address_name"))
                    ->required(),

                Forms\Components\Select::make("user_id")
                    ->label(__("forms.fields.customer_name"))
                    ->relationship('customer', 'name')
                    ->required(),
                Forms\Components\Select::make("city_id")
                    ->formatStateUsing(fn($record)=>$record?->district?->city_id)
                    ->label(__("menu.city"))
                    ->live()
                    ->options(City::pluck("name","id"))
                    ->required(),
                Forms\Components\Select::make("district_id")

                    ->label(__("menu.district"))
                    ->options(fn($get)=>District::where('city_id',$get("city_id"))->pluck("name","id"))
                    ->required(),
                TextInput::make('phone')
                    ->required()
                    ->type('number'),

                Map::make('map_location')

                    ->defaultLocation([24.7136, 46.6753])
                    ->draggable()
                    ->clickable()
                    ->geolocate()
                    ->geolocateLabel('Get Location')
                ,
                Forms\Components\Textarea::make('notes'),


            ])->columns(1);
    }

    public static function table(Table $table): Table {
        return $table
            ->columns([
                TextColumn::make('id')->searchable(),
                TextColumn::make('customer.name')->searchable(),
                TextColumn::make('name')->searchable(),
                TextColumn::make('district.name')->searchable(),
                TextColumn::make('phone')->searchable(),

            ])
            ->filters([
                TernaryFilter::make('primary')

            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array {
        return [
            //
        ];
    }

    public static function getPages(): array {
        return [
            'index' => Pages\ListAddressBooks::route('/'),

        ];
    }
    public static function getPluralLabel(): ?string {
        return __('menu.address_books');
    }

    public static function getLabel(): ?string {
        return __('menu.address_book');
    }
    public static function getNavigationGroup(): ?string {
        return __('menu.crm');
    }
}
