<?php

namespace Tasawk\Filament\Resources\Employees\WorkerResource\Pages;

use Tasawk\Filament\Resources\Employees\WorkerResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditWorker extends EditRecord
{
    protected static string $resource = WorkerResource::class;
   

    // protected function afterSave(): void
    // {
    //     $this->record->worker->deposit(10);
    //     $this->record->worker->withdraw(1);
    //     dd($this->record->worker->transactions()->latest()->paginate());
    // }
    protected function getHeaderActions(): array {
        return [
           
        ];
    }
    protected function getRedirectUrl(): string {
        return $this->getResource()::getUrl("index");
    }
}
