<?php

namespace Tasawk\Filament\Resources\Employees;

use <PERSON><PERSON>hanSalleh\FilamentShield\Contracts\HasShieldPermissions;
use Cheesegrits\FilamentGoogleMaps\Fields\Map;
use Filament\Forms\Components\CheckboxList;
use Filament\Forms\Components\Hidden;
use Filament\Forms\Components\Section as SectionComponent;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\SpatieMediaLibraryFileUpload;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Form;
use Filament\Infolists\Components\Grid;
use Filament\Infolists\Components\Section;
use Filament\Infolists\Components\TextEntry;
use Filament\Infolists\Infolist;
use Filament\Resources\Concerns\Translatable;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Actions\Action;
use Filament\Tables\Columns\IconColumn;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Filters\Filter;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Validation\Rules\Enum;
use Illuminate\Validation\Rules\Password;
use MatanYadaev\EloquentSpatial\Objects\Point;
use Tasawk\Enum\AccountTypeEnum;
use Tasawk\Enum\ModelStatus;
use Tasawk\Enum\NationalityEnum;
use Tasawk\Enum\ResidenceTypeEnum;
use Tasawk\Filament\Resources\Employees\WorkerResource\Pages;
use Tasawk\Filament\Resources\Employees\WorkerResource\RelationManagers\WorkerOrdersRelationManager;

;

use Tasawk\Lib\Options;
use Tasawk\Models\Category;
use Tasawk\Models\Location\City;
use Tasawk\Models\Worker;
use Tasawk\Models\Zone;
use Tasawk\Rules\KSAPhoneRule;
use Tasawk\Traits\Filament\HasTranslationLabel;

class WorkerResource extends Resource implements HasShieldPermissions {
    use HasTranslationLabel, Translatable;

    protected static ?int $navigationSort = 2;
    protected static ?string $model = Worker::class;

    protected static ?string $navigationIcon = 'heroicon-o-users';
    protected static ?string $recordTitleAttribute = 'name';

    public static function form(Form $form): Form {
        return $form
            ->schema([
                SectionComponent::make("basic_information")->schema([

                    TextInput::make('name')
                        ->required()
                        ->columnSpanFull()
                        ->label(__('forms.fields.name')),
                    TextInput::make('email')
                        ->rules(["required", 'email'])
                        ->unique(ignoreRecord: true)
                        ->autocomplete("off"),

                    TextInput::make('phone')
                        ->prefix("+966")
                        ->required()
                        ->rules(["required", new KSAPhoneRule()])
                        ->unique(ignoreRecord: true)
                        ->autocomplete("off"),
                    Select::make('city_id')
                        ->label(__('forms.fields.city_name'))
                        ->required()
                        ->options(fn($get) => City::pluck('name', 'id'))
                        ->searchable(['name->ar', 'name->en']),

                    TextInput::make('password')
                        ->rules([
                            Password::min(8)
                                ->mixedCase()
                                ->letters()
                                ->numbers()
                                ->symbols()
                        ])
                        ->password()
                        ->required(fn(string $operation): bool => $operation === 'create')
                        ->confirmed()
                        ->dehydrated(fn($state) => filled($state))
                        ->autocomplete("new-password"),
                    TextInput::make('password_confirmation')
                        ->password()
                        ->required(fn(string $operation): bool => $operation === 'create')
                        ->autocomplete("off"),
                    Toggle::make('active')->default(1)
                        ->onColor('success')
                        ->offColor('danger'),


                ])
                    ->columnSpan(3),
                SectionComponent::make("additional_data")->schema([

                    Hidden::make('status')->default('approved'),
                    SpatieMediaLibraryFileUpload::make('image')
                        ->collection('logo')
                        ->label(__('forms.fields.image'))
                        ->required()
                        ->columnSpanFull(),
                    TextInput::make('code')->required(),
                    Toggle::make('active')
                        ->label(__('forms.fields.is_active'))
                        ->default(1)
                        ->onColor('success')
                        ->offColor('danger'),

                    Select::make('account_type')
                        ->required()
                        ->live()
                        ->options(fn() => Options::forEnum(AccountTypeEnum::class)->toSelect()),
                    Select::make('nationality')
                        ->visible(fn($get) => $get('account_type') == 'individual')
                        ->required()
                        ->live()
                        ->options(fn() => Options::forEnum(NationalityEnum::class)->toSelect()),

                    Select::make('residence_type')
                        ->live()
                        ->visible(fn($get) => $get('account_type') == 'individual' && $get("nationality") == 'other')
                        ->required()
                        ->options([
                            'professional' => __("panel.enums.PROFESSIONAL"),
                            'individual' => __("panel.enums.INDIVIDUAL"),
                        ]),

                    Textarea::make('bank_account')
                        ->label(__('forms.fields.bank_account'))
                        ->required()
                        ->columnSpanFull(),

                    TextInput::make('commercial_registration_number')
                        ->visible(fn($get) => $get('account_type') == 'company' || $get('residence_type') == 'professional' && $get('nationality') == 'other')
                        ->required(),
                    SpatieMediaLibraryFileUpload::make('commercial_registration_image')
                        ->collection('commercial_registration_image')
                        ->visible(fn($get) => $get('account_type') == 'company' || $get('residence_type') == 'professional' && $get('nationality') == 'other')
                        ->required(),

                    TextInput::make('job_title')
                        ->visible(fn($get) => $get('nationality') == 'other')
                        ->required(),

                    TextInput::make('bondsman_name')
                        ->visible(fn($get) => $get('nationality') == 'other')
                        ->required(),

                    TextInput::make('bondsman_phone')
                        ->rules([new KSAPhoneRule()])
                        ->visible(fn($get) => $get('nationality') == 'other')
                        ->required(),

                    TextInput::make('organization_name')
                        ->visible(fn($get) => $get('nationality') == 'other' && $get('residence_type') == 'professional')
                        ->required(),


                    CheckboxList::make('categories')
                        ->required()
                        ->relationship('categories')
                        ->options(Category::pluck('name', 'id')),
                    SpatieMediaLibraryFileUpload::make('residence_image')
                        ->visible(fn($get) => $get('nationality') == 'other')
                        ->collection('residence_image'),

                    SpatieMediaLibraryFileUpload::make('approve_letter_image')
                        ->required()
                        ->collection('approve_letter_image'),

                    Map::make("location")
                        ->defaultLocation([24.7136, 46.6753])
                        ->formatStateUsing(function ($record) {
                            if (!$record) return;
                            return [
                                'lat' => Point::fromWkt($record->location)->latitude,
                                'lng' => Point::fromWkt($record->location)->longitude,
                            ];
                        })
                        ->defaultZoom(12)
                        ->draggable()
                        ->required()
                        ->clickable()
                        ->required()
                        ->geolocate(),
                ])->relationship("fleetAccount")
                    ->columnSpan(2)
            ])->columns(5);

    }

    public static function table(Table $table): Table {
        return $table
            ->columns([
                TextColumn::make('index')->rowIndex(),
                TextColumn::make('id'),
                TextColumn::make('name')->searchable()->label(__('forms.fields.name')),
                TextColumn::make('phone'),
                TextColumn::make('fleetAccount.account_type')->label(__("forms.fields.account_type")),
                TextColumn::make('fleetAccount.nationality')->label(__("forms.fields.nationality")),

                TextColumn::make('city.name'),
                TextColumn::make('created_at')->date(),
                IconColumn::make('active')
                    ->boolean()
                    ->action(
                        Action::make('Active')
                            ->label(fn(Model $record): string => $record->active ? __('panel.messages.deactivate') : __('panel.messages.activate'))
                            // ->disabled(fn(Model $record): bool => !auth()->user()->can('update', static::getModel()))
                            ->requiresConfirmation()
                            // ->disabled(fn(Model $record): bool => !auth()->user()->can('update', static::getModel()))
                            // ->action(fn(Model $record) => $record->toggleActive())
                            ->action(fn(Model $record) => $record->toggleActive())
                    ),
                IconColumn::make('fleetAccount.active')
                    ->label(__('forms.fields.is_active'))
                    ->action(
                        Action::make('Active')
                            ->label(fn(Model $record): string => $record->fleetAccount->active ? __('panel.messages.deactivate') : __('panel.messages.activate'))
                            ->requiresConfirmation()
                            ->action(fn(Model $record) => $record->fleetAccount()->update(['active' => !$record->fleetAccount->active]))
                    )
                    ->boolean(),

                TextColumn::make('fleetAccount.status')
                    ->label(__("forms.fields.status"))
                    ->formatStateUsing(fn($record) => __("panel.enums." . $record->fleetAccount->status)),

            ])
            ->filters([
                Filter::make('code')
                    ->form([
                        TextInput::make('code'),
                    ])
                    ->query(function ($query, array $data) {
                        return $query
                            ->when(
                                $data['code'],
                                fn($query, $code) => $query->whereHas('fleetAccount', fn($q) => $q->where('code', $code)),
                            );
                    }),

                SelectFilter::make('city_id')
                    ->searchable()
                    ->options(City::pluck('name', 'id')),

                SelectFilter::make('active')->options(ModelStatus::class),


            ])
            ->actions([
//                Tables\Actions\ViewAction::make(),
                Action::make('accept')->action(fn($record) => $record->accept())
                    ->visible(fn($record) => $record->fleetAccount?->status == 'pending')
                    ->label(__('panel.actions.accept'))
                    ->icon('heroicon-o-check-circle')
                    ->color('success'),
                Action::make('reject')->action(fn($record) => $record->reject())
                    ->visible(fn($record) => $record->fleetAccount?->status == 'pending')
                    ->label(__('panel.actions.reject'))
                    ->icon('heroicon-o-x-circle')
                    ->color('danger'),
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make()

            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    static public function infolist(Infolist $infolist): Infolist {
        return $infolist
            ->schema([
                Grid::make()->schema([
                    Section::make("basic_information")
                        ->schema([
                            TextEntry::make('id'),
                            TextEntry::make('name'),
                            TextEntry::make('city.name'),
                            TextEntry::make('city.zone.name')->label(__("forms.fields.zone_name")),
                            TextEntry::make('phone'),
                            TextEntry::make('active')
                                ->color(fn($record) => $record->active ? 'success' : 'danger')
                                ->formatStateUsing(fn($record) => $record->active ? __("panel.enums.ACTIVE") : __("panel.enums.INACTIVE"))
                                ->badge(),
                        ])->columns(1),

                ])->columns(2)
            ]);
    }

    public static function getRelations(): array {
        return [
//            WorkerOrdersRelationManager::class
        ];
    }

    public static function getPages(): array {
        return [
            'index' => Pages\ListWorker::route('/'),
            'create' => Pages\CreateWorker::route('/create'),
            'edit' => Pages\EditWorker::route('/{record}/edit'),
            'view' => Pages\ViewWorker::route('/{record}/view'),
        ];
    }


    public static function getNavigationBadge(): ?string {
        return static::getModel()::count();
    }

    public static function getPermissionPrefixes(): array {
        return [
            'view_any',
            'view',
            'create',
            'update',
            'delete',
            'delete_any',
        ];
    }
}
