<?php

namespace Tasawk\Http\Resources\Api\Manager\Orders;

use Cknow\Money\Money;
use Illuminate\Http\Resources\Json\JsonResource;
use Tasawk\Brands\Resources\Api\BrandResources;
use Tasawk\Enum\BalanceRequestStatusEnum;
use Tasawk\Fleet\Http\Resources\DriverResource;
use Tasawk\Jobs\Http\Resources\Api\RateResources;
use Tasawk\Orders\Resources\Api\AddressOrderResource;
use Tasawk\Orders\Resources\Api\UserOrderResource;

class WalletWithdrawRequestResource extends JsonResource {

    public function toArray($request) {
        return [
            "id" => $this->id,
            'amount' => Money::parse(floatval($this->amount))->format(),
            'status' => $this->status->getLabel(),
            'status_enum' => $this->status->value,
            $this->mergeWhen($this->status == BalanceRequestStatusEnum::COMPLETED, [
                'receipt_image' => $this->getFirstMediaUrl(),
            ]),
            $this->mergeWhen($this->status == BalanceRequestStatusEnum::REJECTED, [
                'reject_reason' => $this->data['reason'] ?? '',
            ]),

            'created_at' => $this->created_at->format("Y-m-d h:i a")
        ];
    }
}
