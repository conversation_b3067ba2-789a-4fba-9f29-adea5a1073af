<?php

namespace Tasawk\Filament\Resources\Reports\WorkerWalletResource\RelationManagers;

use Blade;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Tabs;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Notifications\Notification;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Actions\Action;
use Filament\Tables\Table;

class TransactionsManager extends RelationManager {
    protected static string $relationship = 'transactions';

    public function form(Form $form): Form {
        return $form
            ->schema([

            ]);
    }

    public function table(Table $table): Table {
        return $table
            ->modifyQueryUsing(fn($query) => $query->where('confirmed', true))
            ->heading(__("sections.transactions"))
            ->columns([
                Tables\Columns\TextColumn::make('id'),
                Tables\Columns\TextColumn::make('type')->formatStateUsing(fn($state) => $state == 'deposit' ? __("panel.enums.deposit") : __("panel.enums.withdraw")),
                Tables\Columns\TextColumn::make('meta.description.' . app()->getLocale())
                    ->label(__("forms.fields.description")),
                Tables\Columns\TextColumn::make('amount'),

            ])
            ->filters([
                //
            ])
            ->headerActions([
                Action::make(__("panel.actions.add_operation"))
                    ->action(function ($data) {
                        $operation = $data['type'];
                        if ($operation =='withdraw' && $data['amount'] > $this->getOwnerRecord()->balance) {
                           Notification::make()
                               ->danger()
                               ->title(__('validation.api.insufficient_balance'))
                               ->send();
                            return;

                        }
                        $this->getOwnerRecord()->holder->$operation($data['amount'], $data['meta']);
                        return;
                    })
                    ->form([
                        Tabs::make()->schema([
                            Tabs\Tab::make(__("panel.languages.english"))->schema([
                                Textarea::make('description.en')->label(__('forms.fields.description'))
                                    ->rows(10)
                                    ->required(),

                            ]),
                            Tabs\Tab::make(__("panel.languages.arabic"))->schema([
                                Textarea::make('description.ar')->label(__('forms.fields.description'))
                                    ->required()
                                    ->rows(10),

                            ])
                        ])->statePath('meta'),
                        TextInput::make('amount')
                            ->required()
                            ->numeric()
                            ->label(__('forms.fields.amount')),
                        Select::make('type')
                            ->required()
                            ->options([
                            'deposit' => __('panel.enums.deposit'),
                            'withdraw' => __('panel.enums.withdraw')
                        ])->label(__('forms.fields.operation_type')),


                    ])
            ])
            ->actions([
            ]);
    }


}
