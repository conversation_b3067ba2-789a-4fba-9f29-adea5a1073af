<?php

namespace Tasawk\Filament\Resources\Catalog;

use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\SpatieMediaLibraryFileUpload;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Form;
use Filament\Forms\Get;
use Filament\Infolists\Components\TextEntry;
use Filament\Infolists\Infolist;
use Filament\Notifications\Notification;
use Filament\Resources\Concerns\Translatable;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Actions\Action;
use Filament\Tables\Columns\IconColumn;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Collection;
use Tasawk\Enum\ModelStatus;
use Tasawk\Filament\Resources\Catalog;
use Tasawk\Filament\Resources\Catalog\CategoryResource\RelationManagers\ChildrenRelationManager;
use Tasawk\Models\Catalog\Product;
use Tasawk\Models\Category;
use Tasawk\Traits\Filament\HasTranslationLabel;

class CategoryResource extends Resource {
    use Translatable;
    use HasTranslationLabel;

    protected static ?string $model = Category::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';
    protected static ?int $navigationSort = 4;

    public static function form(Form $form): Form {
        return $form
            ->schema([
                Section::make("basic_information")
                    ->schema([
                        TextInput::make('name')
                            ->required(),

                        SpatieMediaLibraryFileUpload::make('image')
                            ->image()
                            ->required(),
                        Select::make("parent_id")
                            ->label(__('forms.fields.category_parent_id'))
                            ->options(fn(Get $get): Collection => Category::where('id', "!=", $get('id'))
                                ->pluck('name', 'id')),
                        TextInput::make('sort')
                            ->required(),
                        Toggle::make('status')
                            ->default(1)
                            ->onColor('success')
                            ->offColor('danger')
                    ])
            ])->columns(1);
    }

    public static function table(Table $table): Table {
        return $table
            ->columns([
                TextColumn::make('id'),
                TextColumn::make('name'),
                IconColumn::make('status')
                    ->boolean()
                    ->action(
                        Action::make('Active')
                            ->label(fn(Category $record): string => $record->status ? __('panel.messages.deactivate') : __('panel.messages.activate'))
                            ->disabled(fn(Model $record): bool => !auth()->user()->can('update', $record))
                            ->requiresConfirmation()
                            ->action(fn(Category $record) => $record->toggleStatus())

                    )
            ])
            ->filters([
                SelectFilter::make('status')
                    ->options(ModelStatus::class)
            ])
            ->actions([
//                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make()
                    ->before(function (Tables\Actions\DeleteAction $action, Category $category) {
                        if ($category->products()->count()) {
                            Notification::make()
                                ->warning()
                                ->title(__('panel.messages.warning'))
                                ->body(__('panel.messages.category_has_many_products', ['category' => $category->name]))
                                ->persistent()
                                ->send();
                            $action->cancel();
                        }
                    }),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->emptyStateActions([
                Tables\Actions\CreateAction::make(),
            ]);
    }

    static public function infolist(Infolist $infolist): Infolist {
        return $infolist
            ->schema([
                Section::make()
                    ->schema([
                        TextEntry::make('name'),

                        TextEntry::make('status')
                            ->formatStateUsing(fn(string $state): string => $state ? 'Yes' : 'No')
                            ->color(fn(string $state): string => match ($state) {
                                '1' => 'success',
                                '0' => 'danger',
                            })
                    ]),

            ]);
    }

    public static function getRelations(): array {
        return [
            ChildrenRelationManager::class
        ];
    }

    public static function getPages(): array {
        return [
            'index' => Catalog\CategoryResource\Pages\ListCategories::route('/'),
            'create' => Catalog\CategoryResource\Pages\CreateCategory::route('/create'),
            'edit' => Catalog\CategoryResource\Pages\EditCategory::route('/{record}/edit'),
//            'view' => Catalog\CategoryResource\Pages\ViewCategory::route('/{record}'),
        ];
    }

}
