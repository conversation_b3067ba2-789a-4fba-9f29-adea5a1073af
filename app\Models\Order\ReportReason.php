<?php

namespace Tasawk\Models\Order;

use Illuminate\Database\Eloquent\Model;
use Spatie\Translatable\HasTranslations;
use Tasawk\Enum\ReasonForEnum;
use Tasawk\Traits\Publishable;

class ReportReason extends Model {
    use  Publishable, HasTranslations;

    protected array $translatable = ['name'];
    protected $fillable = ['name', 'status','for'];
    public function scopeCustomer($builder) {
        return $builder->where('for', ReasonForEnum::CUSTOMER->value);
    }
    public function scopeWorker($builder) {
        return $builder->where('for', ReasonForEnum::WORKER->value);
    }
}
