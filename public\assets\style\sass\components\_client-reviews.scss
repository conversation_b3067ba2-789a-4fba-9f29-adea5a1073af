.client-reviews {
  overflow: hidden;
  margin-top: 70px;
  .mySwiper-parent {
    padding-bottom: 35px;
    padding-top: 20px;
  }
  .swiper-slide {
    opacity: 0.5;
  }
  .swiper-slide-active,
  .swiper-slide-next {
    opacity: 1;
  }

  .swiper-pagination {
    display: none;
  }
  .swiper {
    overflow: unset;
  }
  .reviewer {
    display: flex;
    align-items: center;
    margin-top: 30px;
    column-gap: 12px;
  }
  .swiper-buttons {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-top: 30px;
    .swiper-button-next:after,
    .swiper-button-prev:after {
      display: none;
    }
    div {
      position: unset;
      margin: 0;
      width: 45px;
      height: 45px;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 50%;
      filter: drop-shadow(6.018px 7.986px 12.5px rgba(0, 0, 0, 0.1));
      background-color: #ffffff;
      transition: all ease-in-out 0.2s;
      &:hover {
        background-color: $orange-color;
        i {
          color: #fff;
        }
      }
      i {
        font-size: 16px;
        color: #999999;
      }
    }
  }
  .content {
    position: relative;
    padding-inline-end: 20px;
    padding-top: 30px;
    z-index: 5;
    background: #fff;
    height: 100%;
    &::after {
      position: absolute;
      content: "";
      top: 0;
      inset-inline-start: 0;
      width: 110%;
      background-color: #fff;
      inset-inline-start: -110%;
      height: 100%;
      z-index: -1;
    }
  }
  .general-title {
    text-align: start;
    line-height: 50px;
  }
  .sub-title {
    text-align: start;
    margin-bottom: 30px;
  }
  .item {
    width: 361px;
    padding: 31px 25px;
    filter: drop-shadow(6.018px 7.986px 12.5px rgba(0, 0, 0, 0.1));
    background-color: #ffffff;
    .icon {
      font-size: 48px;
      color: $orange-color;
    }
    .quote-text {
      font-size: 16px;
      line-height: 30px;
      font-weight: 400;
      color: #000;
      text-align: start;
      margin-top: 15px;
      margin-bottom: 0;
    }
    .reviewer-img {
      width: 50px;
      height: 50px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: #ededed;
      i {
        font-size: 20px;
        color: #999999;
      }
    }
    .name {
      font-size: 16px;
      color: #000;
      font-weight: 500;
    }
  }
  .swiper-slide-prev {
    .item {
      filter: none;
    }
  }
}

html[dir="ltr"] {
  .client-reviews .swiper-buttons div i {
    transform: rotate(180deg);
  }
}

@include max-992 {
  .client-reviews {
    .swiper-pagination {
      display: flex;
      bottom: 35px;
      justify-content: center;
      .swiper-pagination-bullet {
        opacity: 0.302;
        background-color: #000;
        width: 6px;
        height: 6px;
      }
      .swiper-pagination-bullet-active {
        background-color: $orange-color;
        opacity: 1;
      }
    }
    .row {
      flex-direction: column;
      align-items: center;
      row-gap: 30px;
    }
    .sub-title {
      text-align: center;
    }
    .general-title {
      text-align: center;
      font-size: 30px;
    }
    .swiper-buttons {
      justify-content: center;
    }
    .content::after {
      display: none;
    }
    .swiper {
      overflow: hidden;
      padding: 20px;
    }
    .item {
      width: 100%;
      filter: drop-shadow(2.018px 3.986px 6.5px rgba(0, 0, 0, 0.1));
    }
    .swiper-slide-prev .item {
      filter: drop-shadow(2.018px 3.986px 6.5px rgba(0, 0, 0, 0.1));
    }
    .mySwiper-parent {
      padding: 0;
      padding-bottom: 50px;
    }
    .swiper-slide-prev {
      opacity: 1;
    }
  }
  .swiper-slide {
    opacity: 1;
  }
}

@include max-768 {
  .client-reviews {
    .content {
      padding-top: 0;
    }
    .swiper-slide {
      display: flex;
      justify-content: center;
    }
    .general-title {
      font-size: 21px;
      line-height: 1.5;
    }
    margin-top: 25px;
    .swiper-buttons {
      display: none;
    }
    .sub-title {
      margin-bottom: 20px;
    }
    .item {
      filter: drop-shadow(2.018px 2.986px 6.5px rgba(0, 0, 0, 0.1));
    }
    .swiper-slide-next .item,
    .swiper-slide-prev .item {
      filter: none;
    }
    .swiper {
      padding: 17px 12px;
    }
    .row > * {
      padding: 0;
    }
  }
}
