<?php

namespace Tasawk\Http\Resources\Api\Manager\Orders;

use Carbon\Carbon;
use Cknow\Money\Money;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Str;
use Tasawk\Actions\GetDistanceBetweenLocationsAction;
use Tasawk\Enum\OrderStatus;
use Tasawk\Http\Resources\Api\Customer\AddressBookResource;
use Tasawk\Http\Resources\Api\Customer\Cart\CartResource;
use Tasawk\Http\Resources\Api\Customer\RateResource;
use Tasawk\Http\Resources\Api\Shared\CategoryResource;
use Tasawk\Http\Resources\Api\Shared\WorkerTypeResource;

class OrdersResource extends JsonResource {

    /**
     * Transform the resource into an array.
     *
     * @param Request $request
     * @return array
     */
    public function toArray($request) {
        $distance = GetDistanceBetweenLocationsAction::run(
            ['lat' => $this->address?->map_location['lat'] ?? 0, 'lng' => $this->address?->map_location['lng']??0],
            ['lat' => $this->worker?->fleetAccount?->location?->getCoordinates()[1]??0, 'lng' => $this->worker?->fleetAccount?->location?->getCoordinates()[0]??0],
        );
        $request->merge(['distance' => $distance]);
        $cart = $this->as_cart;
        return [
            "id" => $this->id,
            "status" => $this->getRenderStatus(),
            "status_code" => $this->status,
            "created_date" => $this->created_at?->format("Y-m-d h:i a"),
            "date" => $this->date?->format("Y-m-d") ?? __("Not yet determined"),
            'time' => $this->time->format("h:i a"),
            $this->mergeWhen($this->status == OrderStatus::COMPLETED, [
                'invoice_url' => route('orders.invoice.download', $this->id),
            ]),
            'service'=>[
                'id' => $this->service?->id,
                'title' => $this->service?->title,
                'price' => Money::parse($this->itemsline?->first()?->price)->format(),
            ],
            'category' => CategoryResource::make($this->category),
            'sub_category' => CategoryResource::make($this->subCategory),
            'worker_type' => $this->worker_type->getLabel(),
            $this->mergeWhen($this->status != OrderStatus::PENDING, [
                'customer' => CustomerResource::make($this->customer, ['distance' => 1]),
                'address' => AddressBookResource::make($this?->address),
            ]),


            'images' => $this->getMedia()->map(fn($media) => $media->getFullUrl()),
            $this->mergeWhen($this->cancellation()->exists(), [
                'cancellation_reason' => $this->cancellation?->reason?->name
            ]),

            $this->mergeWhen($this?->rated(), [
                'rating' => RateResource::make($this?->rate)
            ]),
            $this->mergeWhen($this?->report()->exists(), [
                'problem_text' => $this?->report->reason?->name ?? $this?->report?->note
            ]),
            'can' => [
                'pricing' => $this->canSetPrice(),
                'report' => $this->canReport(),
                'accept' => $this->status == OrderStatus::PENDING && !$this->workers->pluck('id')->contains(auth()->id()),
            ],

            'notes' => $this->notes,
            'cart' => CartResource::make($cart),

        ];
    }
}
