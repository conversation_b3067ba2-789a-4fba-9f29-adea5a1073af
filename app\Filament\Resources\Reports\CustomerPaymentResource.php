<?php

namespace Tasawk\Filament\Resources\Reports;

use Filament\Infolists\Components\TextEntry;
use Filament\Tables\Columns\TextColumn;
use Illuminate\Support\Str;
use pxlrbt\FilamentExcel\Actions\Tables\ExportBulkAction;
use pxlrbt\FilamentExcel\Exports\ExcelExport;
use Tasawk\Filament\Resources\OrderResource;
use Tasawk\Filament\Resources\Reports\CustomerPaymentResource\Pages\ListCustomerPayments;
use Tasawk\Filament\Resources\Reports\ProfitsResource\Pages;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;

use Tasawk\Models\Order;
use Tasawk\Traits\Filament\HasTranslationLabel;

class CustomerPaymentResource extends Resource {
    use HasTranslationLabel;

    protected static ?string $model = Order::class;

    protected static ?string $navigationIcon = 'heroicon-o-chart-pie';

    public static function form(Form $form): Form {
        return $form
            ->schema([
                //
            ]);
    }

    public static function table(Table $table): Table {
        return $table
            ->modifyQueryUsing(fn($query) => $query->paid())
            ->columns([

                TextColumn::make('order_number')->searchable(['id']),

                TextColumn::make('date')
                    ->date()
                    ->searchable(),
                TextColumn::make('time')
                    ->time()
                    ->searchable(),
                TextColumn::make('customer.name')->searchable(),
                TextColumn::make('customer.phone')
                    ->label(__('forms.fields.phone'))
                    ->searchable(),
                TextColumn::make('order_number')
                    ->searchable(['id'])
                    ->url(fn($record) => OrderResource::getUrl('view',[$record]), true),
                TextColumn::make('total')
                    ->money()
                    ->searchable(),
                TextColumn::make('invoice_url')
                    ->state(fn($record) =>__("forms.fields.show_invoice"))
                    ->url(fn($record) => $record->payment_data['invoiceURL']??'', true)
                    ->searchable(false),

                TextColumn::make('total')
                    ->formatStateUsing(fn($record) => $record->total->format())
                    ->summarize(Tables\Columns\Summarizers\Sum::make()->money('SAR')),
            ])
            ->filters([
                //
            ])
            ->actions([
            ])
            ->bulkActions([

                Tables\Actions\BulkActionGroup::make([
                    ExportBulkAction::make()->exports([
                        ExcelExport::make("CSV")
                            ->fromTable()
                            ->withFilename(fn() => static::getPluralLabel() . '-' . now()->format('Y-m-d'))
                            ->withWriterType(\Maatwebsite\Excel\Excel::XLSX),


                    ]),
                ]),
            ]);
    }

    public static function getRelations(): array {
        return [
            //
        ];
    }

    public static function getPages(): array {
        return [
            'index' => ListCustomerPayments::route('/'),
        ];
    }

    public static function getPluralLabel(): ?string {
        return __('menu.customer_payments');
    }

    public static function getNavigationGroup(): ?string {
        return __('menu.reports');
    }

}
