<?php

namespace Tasawk\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Tasawk\Models\Location\City;
use Spatie\MediaLibrary\InteractsWithMedia;

class Worker extends User {
    use HasFactory, InteractsWithMedia;

    protected $table = "users";
    protected string $guard_name = 'web';
    const  ROLE = 'worker';


    public function getMorphClass(): string {
        return User::class;
    }

    protected static function booted() {
        parent::booted();
        static::creating(fn($model) => $model->assignRole(self::ROLE));
        static::addGlobalScope("Worker", function ($builder) {
            $builder->whereHas("roles", fn($q) => $q->where('name', self::ROLE));
        });
    }


    public function city() {
        return $this->belongsTo(City::class);
    }


    public function isEnabledNotification() {
        return $this->settings['notification_status'] ?? 1;
    }

    public function scopeWhereNotificationsModeEnabled($builder) {
        return $builder->where('settings->notification_status', 1)->orWhereNull('settings->notification_status');
    }

    public function orders() {
        return $this->hasMany(Order::class, 'worker_id');
    }

    public function accept(): void {
        $this->update(['active' => 1]);
        $this->fleetAccount()->update(['status' => 'approved']);
    }

    public function reject(): void {
        $this->update(['active' => 0]);
        $this->fleetAccount()->update(['status' => 'rejected']);
    }

}



