<?php

namespace Tasawk\Enum;

use Filament\Support\Contracts\HasLabel;

enum ReasonForEnum: string implements HasLabel {
    case CUSTOMER = 'customer';
    case WORKER = 'worker';




    public function getLabel(): ?string {
        return __("panel.enums.$this->value");
    }

    public function getColor(): string {
        return match ($this->value) {
            'customer', => 'warning',
            'worker', => 'primary',

        };

    }

}
