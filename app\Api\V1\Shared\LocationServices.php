<?php

namespace Tasawk\Api\V1\Shared;
use Tasawk\Api\Facade\Api;
use Tasawk\Http\Resources\Api\Shared\ZoneResource;
use Tasawk\Models\Location\City;
use Tasawk\Models\Zone;

class LocationServices {
    public function zones() {
        return Api::isOk("Zones list", ZoneResource::collection(Zone::enabled()->get()));
    }

    public function cities() {
        return Api::isOk("Cities list", ZoneResource::collection(City::enabled()->get()));
    }

    public function districts(City $city) {
        return Api::isOk("Cities list", ZoneResource::collection($city->districts()->enabled()->get()));
    }


}
