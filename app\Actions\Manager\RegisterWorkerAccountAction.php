<?php

namespace Tasawk\Actions\Manager;

use Lorisle<PERSON>\Actions\Concerns\AsAction;
use MyFatoorah\Library\MyfatoorahApiV2;
use MyFatoorah\Library\PaymentMyfatoorahApiV2;
use Tasawk\Models\Order;
use Tasawk\Models\User;
use Tasawk\Models\Worker;

class RegisterWorkerAccountAction {
    use AsAction;

    public function handle($data) {
        $worker = $this->createUseAccount($data['name'], $data['email'], $data['phone'], $data['city_id'], $data['password']);
        $fleetAccount = $worker->fleetAccount()->create([
            'account_type' => $data['account_type'],
            'nationality' => $data['nationality']??'',
            'residence_type' => $data['residence_type'] ?? '',
            'location' => $data['location'],
            'job_title' => $data['job_title'] ?? '',
            'bondsman_name' => $data['bondsman_name'] ?? '',
            'bondsman_phone' => $data['bondsman_phone'] ?? '',
            'bank_account' => $data['bank_account'],
            'commercial_registration_number' => $data['commercial_registration_number'] ?? '',
            'organization_name' => $data['organization_name'] ?? '',
            'status' => 'pending',
            'code'=>$data['code']??'',

        ]);
        if (isset($data['commercial_registration_image'])) {
            $fleetAccount->addMedia($data['commercial_registration_image'])->toMediaCollection('commercial_registration_image');
        }
        if (isset($data['residence_image'])) {
            $fleetAccount->addMedia($data['residence_image'])->toMediaCollection('residence_image');
        }
        if (isset($data['logo'])) {
            $fleetAccount->addMedia($data['logo'])->toMediaCollection('logo');
        }
        if (isset($data['approve_letter_image'])) {
            $fleetAccount->addMedia($data['approve_letter_image'])->toMediaCollection('approve_letter_image');
        }
        $fleetAccount->categories()->sync($data['categories']);

        return $worker;
    }

    public function createUseAccount($name, $email, $phone, $city, $password) {
        return Worker::create([
            'name' => $name,
            'email' => $email,
            'phone' => $phone,
            'city_id' => $city,
            'password' => $password,
            'active' => 0
        ]);
    }

}
