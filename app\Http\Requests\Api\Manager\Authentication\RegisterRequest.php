<?php

namespace Tasawk\Http\Requests\Api\Manager\Authentication;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;
use Tasawk\Rules\KSAPhoneRule;

class RegisterRequest extends FormRequest {
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize() {
        return true;
    }

    protected function prepareForValidation() {

    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules() {
        return [
            "account_type" => ['required', 'in:individual,company'],
            "nationality" => [Rule::requiredIf($this->get('account_type') == 'individual'), 'in:saudi,other'],
            "residence_type" => ['nullable', Rule::requiredIf($this->get('account_type') == 'individual' && $this->get('nationality') == 'other'), 'in:professional,individual'],
            'logo' => [
                'required',
                'image',
                'mimes:jpeg,png,jpg,gif,svg',
                'max:2048'
            ],
            'name' => ['required', 'string', 'min:3'],
            'email' => ['required', 'email', 'unique:users,email'],
            'phone' => ['required', new KSAPhoneRule(), 'unique:users,phone'],
            'city_id' => ['required', 'exists:cities,id'],
            'password' => [
                'required',
            ],
            'location' => ['required', 'array'],
            'location.lat' => ['required', 'numeric'],
            'location.lng' => ['required', 'numeric'],
            'categories' => ['required', 'array'],
            'categories.*' => ['required', 'exists:categories,id'],
            'job_title' => [
                Rule::requiredIf(fn() => $this->nationality == 'other')
                , 'string'],
            'bondsman_name' => [
                Rule::requiredIf(fn() => $this->nationality == 'other')
                , 'string'],
            'bondsman_phone' => [
                Rule::requiredIf(fn() => $this->nationality == 'other')
                , new KSAPhoneRule()
            ],
            'organization_name' => [
                Rule::requiredIf(fn() => $this->residence_type == 'professional' && $this->account_type != 'company' && $this->nationality == 'other')
                , 'string'],
            'bank_account' => ['required', 'string'],
            'commercial_registration_number' => [
                Rule::requiredIf(fn() => $this->account_type == 'company' || $this->residence_type == 'professional' && $this->nationality == 'other')
                , 'string'],
            'commercial_registration_image' => [
                Rule::requiredIf(fn() => $this->account_type == 'company' || $this->residence_type == 'professional' && $this->nationality == 'other'),
                'image',
                'mimes:jpeg,png,jpg,gif,svg',
                'max:2048'
            ],
            'residence_image' => [
                Rule::requiredIf(fn() => $this->nationality == 'other')
                , 'image', 'mimes:jpeg,png,jpg,gif,svg', 'max:2048'],
            'approve_letter_image' => [
                'required'
                , 'image', 'mimes:jpeg,png,jpg,gif,svg', 'max:2048'],

            'code' => ['nullable']
        ];
    }


}
