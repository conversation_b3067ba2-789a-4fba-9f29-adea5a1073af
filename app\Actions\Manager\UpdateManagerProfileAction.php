<?php

namespace Tasawk\Actions\Manager;

use Lorisleiva\Actions\Concerns\AsAction;
use MatanYadaev\EloquentSpatial\Objects\Point;

class UpdateManagerProfileAction {
    use AsAction;

    public function handle($data) {
        auth()->user()->update(collect($data)->only('name', 'email', 'phone', 'city_id')->toArray());
        $fleetAccount = auth()->user()->fleetAccount();
        $fleetAccount->update([
            'location' => (new Point($data['location']['lat'], $data['location']['lng']))->toSqlExpression($fleetAccount->getConnection()),
            'job_title' => $data['job_title'] ?? '',
            'bondsman_name' => $data['bondsman_name'] ?? '',
            'bondsman_phone' => $data['bondsman_phone'] ?? '',
            'bank_account' => $data['bank_account'],
            'commercial_registration_number' => $data['commercial_registration_number'] ?? '',
            'organization_name' => $data['organization_name'] ?? '',
            'status' => 'pending',

        ]);
        if (isset($data['commercial_registration_image'])) {
            $fleetAccount->clearMediaCollection('commercial_registration_image');
            $fleetAccount->addMedia($data['commercial_registration_image'])->toMediaCollection('commercial_registration_image');
        }
        if (isset($data['residence_image'])) {
            $fleetAccount->clearMediaCollection('residence_image');
            $fleetAccount->addMedia($data['residence_image'])->toMediaCollection('residence_image');
        }
        if (isset($data['logo'])) {
            $fleetAccount->clearMediaCollection('logo');
            $fleetAccount->addMedia($data['logo'])->toMediaCollection('logo');
        }
        if (isset($data['approve_letter_image'])) {
            $fleetAccount->clearMediaCollection('approve_letter_image');
            $fleetAccount->addMedia($data['approve_letter_image'])->toMediaCollection('approve_letter_image');
        }
        auth()->user()->fleetAccount->categories()->sync($data['categories']);
    }
}
