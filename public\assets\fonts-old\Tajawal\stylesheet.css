@font-face {
    font-family: '<PERSON>jawal';
    src: url('Tajawal-Black.woff2') format('woff2'),
        url('Tajawal-Black.woff') format('woff'),
        url('Tajawal-Black.ttf') format('truetype');
    font-weight: 900;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: 'Tajawal';
    src: url('Tajawal-Bold.woff2') format('woff2'),
        url('Tajawal-Bold.woff') format('woff'),
        url('Tajawal-Bold.ttf') format('truetype');
    font-weight: bold;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: 'Tajawal';
    src: url('Tajawal-ExtraLight.woff2') format('woff2'),
        url('Tajawal-ExtraLight.woff') format('woff'),
        url('Tajawal-ExtraLight.ttf') format('truetype');
    font-weight: 200;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: 'Tajawal';
    src: url('Tajawal-Regular.woff2') format('woff2'),
        url('Tajawal-Regular.woff') format('woff'),
        url('Tajawal-Regular.ttf') format('truetype');
    font-weight: normal;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: 'Tajawal';
    src: url('Tajawal-Light.woff2') format('woff2'),
        url('Tajawal-Light.woff') format('woff'),
        url('Tajawal-Light.ttf') format('truetype');
    font-weight: 300;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: 'Tajawal';
    src: url('Tajawal-ExtraBold.woff2') format('woff2'),
        url('Tajawal-ExtraBold.woff') format('woff'),
        url('Tajawal-ExtraBold.ttf') format('truetype');
    font-weight: bold;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: 'Tajawal';
    src: url('Tajawal-Medium.woff2') format('woff2'),
        url('Tajawal-Medium.woff') format('woff'),
        url('Tajawal-Medium.ttf') format('truetype');
    font-weight: 500;
    font-style: normal;
    font-display: swap;
}

