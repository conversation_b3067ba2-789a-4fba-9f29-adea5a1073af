<?php

namespace Tasawk\Actions\Manager;

use Lorisleiva\Actions\Concerns\AsAction;
use MyFatoorah\Library\API\Payment\MyFatoorahPayment;
use MyFatoorah\Library\MyfatoorahApiV2;
use MyFatoorah\Library\PaymentMyfatoorahApiV2;
use Tasawk\Models\Order;
use Tasawk\Models\User;

class ChargeWorkerWalletBalanceAction {
    use AsAction;

    public function handle( User $worker,$amount,) {
        $myfatoorah =app(MyFatoorahPayment::class);
        $transaction =$worker->deposit($amount, [
            'description' => [
                'ar' => __("panel.messages.add_wallet_balance", ["amount" => $amount], 'ar'),
                'en' => __("panel.messages.add_wallet_balance", ["amount" => $amount], 'en')
            ]
        ],false);
        return $myfatoorah->getInvoiceURL([
            'CustomerName' => $worker?->name ?? '',
            'InvoiceValue' => $amount,
            'DisplayCurrencyIso' => 'SAR',
            'CustomerEmail' => $worker?->email,
            'CallBackUrl' => route('webhooks.myfatoorah.callback'),
            'ErrorUrl' => route('webhooks.myfatoorah.callback'),
            'MobileCountryCode' => '+965',
            'CustomerMobile' => $worker->phone,
            'Language' => app()->getLocale(),
            'CustomerReference' => $transaction->id,
            'UserDefinedField'=>json_encode([
                'type'=>'wallet',
            ]),

            'SourceInfo' => 'wallet'
        ]);

    }

}
