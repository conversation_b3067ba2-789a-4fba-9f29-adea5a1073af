<?php

namespace Tasawk\Http\Resources\Api\Manager\Wallet;

use Illuminate\Http\Resources\Json\JsonResource;

class TransactionResource extends JsonResource {

    /**
     * Transform the resource into an array.
     *
     * @param \Illuminate\Http\Request $request
     * @return array
     */

    public function toArray($request) {
        return [
            'id' => $this->id,
            'type' => __("panel.enums." . $this->type),
            'amount' => $this->amount,
            'description' => $this->meta['description'][app()->getLocale()] ?? '',
            'meta_data'=>(object)collect($this->meta)->except('description')->toArray(),
        ];
    }
}
