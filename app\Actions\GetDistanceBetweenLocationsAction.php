<?php

namespace Tasawk\Actions;

use Illuminate\Support\Facades\DB;
use Lorisleiva\Actions\Concerns\AsAction;
use MyFatoorah\Library\MyfatoorahApiV2;
use MyFatoorah\Library\PaymentMyfatoorahApiV2;
use Tasawk\Actions\Manager\DepositWorkerDuesAfterOrderCompletedAction;
use Tasawk\Enum\OrderStatus;
use Tasawk\Models\Order;
use Theamostafa\Wallet\Models\Transaction;

class GetDistanceBetweenLocationsAction {
    use AsAction;

    public function handle($coordinate1, $coordinate2) {
       return round(\GeometryLibrary\SphericalUtil::computeDistanceBetween(
                $coordinate1,
                $coordinate2) / 1000, 2) . __("forms.suffixes.km");
    }
}
