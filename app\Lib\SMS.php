<?php

namespace Tasawk\Lib;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Tasawk\Settings\ThirdPartySettings;

class SMS {
    public function __construct(public $phone, public $message) {
    }

    public static function init($phone, $message) {
        return new self($phone, $message);
    }

    public function send() {
        
        $res = Http::get('http://mora-sa.com/api/v1/sendsms', [
            'username' => "1068149176",
            'sender' => "ManzlyApp",
            'message' => $this->message,
            'numbers' => $this->phone,
            'response' => 'json',
            'api_key' => "ff783c1f03d6bc3552872c5f8af54352a4ce7d22",
        ])->json();

        Log::info('Mora SMS Response', $res);

    }
}
