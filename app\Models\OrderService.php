<?php

namespace Tasawk\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Cknow\Money\Casts\MoneyDecimalCast;

class OrderService extends Model
{
    protected $table = 'order_services';
    
    protected $fillable = [
        'order_id',
        'service_id',
        'quantity',
        'unit_price',
        'total_price',
    ];
    
    protected $casts = [
        'unit_price' => MoneyDecimalCast::class,
        'total_price' => MoneyDecimalCast::class,
        'quantity' => 'integer',
    ];
    
    public function order(): BelongsTo
    {
        return $this->belongsTo(Order::class);
    }
    
    public function service(): BelongsTo
    {
        return $this->belongsTo(Service::class);
    }
    
    /**
     * Calculate total price based on quantity and unit price
     */
    public function calculateTotalPrice(): void
    {
        $this->total_price = $this->unit_price * $this->quantity;
    }
}
