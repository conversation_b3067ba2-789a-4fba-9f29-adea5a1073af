<!DOCTYPE html>
<html lang="<?php echo e(app()->getLocale()); ?>" dir=<?php echo e(app()->getLocale() == 'en' ? 'ltr' : 'rtl'); ?>>

<head>
    <meta charset="UTF-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="description" content="">
    <meta name="keywords" content="">
    <link rel="icon" href="<?php echo e(asset('assets/images/hoomi-light-logo.svg')); ?>" type="image/webp" />
    <style>
        body {
            --banner-bg: url("<?php echo e($banner); ?>")
        }
    </style>
    <?php echo $__env->make('site.includes.style', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

    <?php echo \Livewire\Mechanisms\FrontendAssets\FrontendAssets::styles(); ?>

    <title><?php echo $__env->yieldContent('title'); ?></title>
</head>

<body data-bs-spy="scroll" data-bs-target="#navbar-example" class=<?php echo $__env->yieldContent('header-page'); ?>>
<!-- preloader -->


    <!-- preloader -->
    <div class="preloader-container">
        <div class="preloader"></div>
      </div>
    <!-- end preloader -->
    <!-- header -->
    <?php echo $__env->make('site.includes.header', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
    <!-- end of header -->

    <?php echo $__env->yieldContent('content'); ?>

    <?php echo $__env->make('site.includes.footer', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
    <!-- to top button  -->
    <button class="toTop">
        <i class="fa-light toTop-icon fa-arrow-up"></i>
    </button>
    <!-- end of to top button -->
    <?php echo $__env->make('site.includes.flash-message', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
    <?php echo $__env->make('site.includes.script', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
    <?php echo \Livewire\Mechanisms\FrontendAssets\FrontendAssets::scripts(); ?>

    <script>
        var dark = "<?php echo e($logo_dark); ?>"
        var light = "<?php echo e($logo_light); ?>"
    </script>
</body>

</html>
<?php /**PATH D:\Workstation\hoomi\resources\views/site/layouts/app.blade.php ENDPATH**/ ?>