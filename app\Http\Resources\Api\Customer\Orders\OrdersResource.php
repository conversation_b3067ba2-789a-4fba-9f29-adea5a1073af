<?php

namespace Tasawk\Http\Resources\Api\Customer\Orders;

use Carbon\Carbon;
use Cknow\Money\Money;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Str;
use Tasawk\Enum\OrderStatus;
use Tasawk\Http\Resources\Api\Customer\AddressBookResource;
use Tasawk\Http\Resources\Api\Customer\Branches\BranchResource;
use Tasawk\Http\Resources\Api\Customer\Branches\LightBranchResource;
use Tasawk\Http\Resources\Api\Customer\Cart\CartProductResource;
use Tasawk\Http\Resources\Api\Customer\RateResource;
use Tasawk\Http\Resources\Api\Customer\ReportResource;
use Tasawk\Http\Resources\Api\Shared\CategoryResource;
use Tasawk\Http\Resources\Api\Shared\ServiceResource;
use Tasawk\Http\Resources\Api\Shared\WorkerTypeResource;
use Tasawk\Settings\GeneralSettings;

class OrdersResource extends JsonResource {

    /**
     * Transform the resource into an array.
     *
     * @param Request $request
     * @return array
     */
    public function toArray($request) {
        $request->merge(['address' => $this->address]);
        $cart = $this->as_cart;
        return [
            "id" => $this->id,
            "status" => $this->getRenderStatus(),
            "status_code" => $this->status,
            "created_date" => $this->created_at?->format("Y-m-d h:i a"),
            "date" => $this->date?->format("Y-m-d") ?? __("Not yet determined"),
            'time' => $this->time->format("h:i a"),
            'service'=>[
                'id' => $this->service?->id,
                'title' => $this->service?->title,
                'price' => Money::parse($this->itemsline?->first()?->price)->format(),
            ],
            'services' => $this->orderServices->map(function ($orderService) {
                return [
                    'id' => $orderService->service->id,
                    'title' => $orderService->service->title,
                    'quantity' => $orderService->quantity,
                    'unit_price' => $orderService->unit_price->format(),
                    'total_price' => $orderService->total_price->format(),
                ];
            }),
            $this->mergeWhen($this->status == OrderStatus::COMPLETED, [
                'invoice_url' => route('orders.invoice.download', $this->id),
            ]),
            'customer_name' => $this->customer?->name,
            'category' => CategoryResource::make($this->category),
            'sub_category' => CategoryResource::make($this->subCategory),
            'worker_type' => $this->worker_type->getLabel(),
            'worker' => WorkerResource::make($this->worker),
            'workers' => WorkerResource::collection($this->workers),
            'payment' => [
                'id' => (int)($this->payment_data['invoiceId'] ?? 0),
                'url' => $this->payment_data['invoiceURL'] ?? null,
                'status' => __("panel.enums." . $this->payment_status->value),
                'status_code' => Str::headline($this->payment_status->value),
                'gateway' => $this->payment_data['gateway'] ?? null,
                'method' => $this->payment_data['method'] ?? null,
                'paid_at' => isset($this->payment_data['paid_at']) ? Carbon::parse($this->payment_data['paid_at'])->timezone('africa/cairo')->format('Y-m-d h:i a') : null,
            ],

            'address' => AddressBookResource::make($this?->address),
            'images' => $this->getMedia()->map(fn($media) => $media->getFullUrl()),
            $this->mergeWhen($this->cancellation()->exists(), [
                'cancellation_text' => $this->cancellation?->reason?->name ?? $this?->cancellation?->note
            ]),

            $this->mergeWhen($this?->rated(), [
                'rating' => RateResource::make($this?->rate)
            ]),
            $this->mergeWhen($this?->report()->exists(), [
                'problem_text' => $this?->report->reason?->name ?? $this?->report?->note
            ]),
            'can' => [
                'accept' => $this->canAccept(),
                'pay' => $this->canPay(),
                'rate' => $this->canRate(),
                'report' => $this->canReport(),
                'cancel' => $this->canCancel(),
            ],
            'notes' => $this->notes,
            "totals" => $cart->formattedTotals(),

        ];
    }
}
