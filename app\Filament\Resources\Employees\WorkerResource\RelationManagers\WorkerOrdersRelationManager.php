<?php

namespace Tasawk\Filament\Resources\Employees\WorkerResource\RelationManagers;

use Filament\Forms;
use Filament\Tables;
use Filament\Forms\Form;
use Tasawk\Models\Order;
use Tasawk\Models\Worker;
use Filament\Tables\Table;
use Tasawk\Enum\OrderStatus;
use Tasawk\Models\WorkerOrder;
use Filament\Tables\Columns\TextColumn;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Tasawk\Filament\Resources\OrderResource;
use Filament\Resources\RelationManagers\RelationManager;

class WorkerOrdersRelationManager extends RelationManager
{
    protected static string $relationship = 'workerOrders';



    public function table(Table $table): Table
    {
        return $table
            ->heading(__('forms.fields.order_list'))
            ->columns([
                TextColumn::make('order.id')->label(__('forms.fields.id')),
                TextColumn::make('order.order_number')->label(__('forms.fields.order_number')),
                TextColumn::make('order.created_at')->date()->label(__('forms.fields.created_at')),
                TextColumn::make('order.order_type')->label(__('forms.fields.order_type')),
                TextColumn::make('order.status')->label(__('forms.fields.status'))
                ->badge()
                ->color(fn($state) => $state->getColor())
                ->formatStateUsing(function ($record) {
                    return $record->status->getLabel() ;
                }),
                TextColumn::make('order.category.name')->label(__('forms.fields.category_name')),
                TextColumn::make('order.customer.name')->label(__('forms.fields.customer_name')),
                TextColumn::make('order.customer.phone')->label(__('forms.fields.phone')),
                TextColumn::make('services')->getStateUsing(function (WorkerOrder $record) {
                    $text = [];
                    foreach ($record->order->services as $service) {
                        // array_push($service->service->name, $text);
                        $text[] =  $service->service->name ;
                    }
                    return $text;
                }),
            ])
            ->filters([
                //
            ])
            ->headerActions([
            ])
            ->actions([
                Tables\Actions\ViewAction::make()->url( fn (Model $record): string => OrderResource::getUrl('view', ['record' => $record->order_id])),

            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    // Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }
}
