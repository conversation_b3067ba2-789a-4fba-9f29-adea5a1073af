<?php

namespace Tasawk\Api\V1\Manager;

use Cknow\Money\Money;
use Tasawk\Actions\Manager\ChargeWorkerWalletBalanceAction;
use Tasawk\Api\Core;
use Tasawk\Api\Facade\Api;
use Tasawk\Enum\OrderStatus;
use Tasawk\Enum\ReceiptMethods;
use Tasawk\Http\Requests\Api\Manager\ChangeOrderStatusRequest;
use Tasawk\Http\Resources\Api\Manager\Orders\LightOrderResource;
use Tasawk\Http\Resources\Api\Manager\Orders\OrdersResource;
use Tasawk\Http\Resources\Api\Manager\ProductOptionsResource;
use Tasawk\Http\Resources\Api\Manager\ProductResource;
use Tasawk\Http\Resources\Api\Manager\Wallet\TransactionResource;
use Tasawk\Http\Resources\Api\Manager\WorkingDayResource;
use Tasawk\Models\CancellationReason;
use Tasawk\Models\Catalog\Branch\Inventory;
use Tasawk\Models\Catalog\Branch\InventoryOption;
use Tasawk\Models\Catalog\Branch\InventoryOptionValue;
use Tasawk\Models\Manager;
use Tasawk\Models\Order;


class WalletService {

    public function index() {
        return Api::isOk("Wallet transactions", TransactionResource::collection(auth()->user()->wallet->transactions()->where('confirmed',true)->latest()->paginate()))
            ->addAttribute('balance',auth()->user()->balance);
    }

    public function charge($amount) {
        return Api::isOk("",ChargeWorkerWalletBalanceAction::run(auth()->user(),$amount))
            ;
    }
}
