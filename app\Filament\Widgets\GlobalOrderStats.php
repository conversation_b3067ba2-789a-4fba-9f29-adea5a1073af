<?php

namespace Tasawk\Filament\Widgets;

use <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\FilamentShield\Traits\HasWidgetShield;
use Cknow\Money\Money;
use DB;
use Filament\Widgets\Concerns\InteractsWithPageTable;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;
use Tasawk\Enum\OrderStatus;
use Tasawk\Filament\Resources\OrderResource\Pages\ListOrders;
use Tasawk\Models\Customer;
use Tasawk\Models\FleetWorker;
use Tasawk\Models\Order;
use Tasawk\Models\Worker;

class GlobalOrderStats extends BaseWidget {
    use HasWidgetShield;

    protected static ?int $sort = 1;

    protected function getStats(): array {
        return [

            Stat::make(__('panel.stats.customer_count'), Customer::count()),
            Stat::make(__('panel.stats.pending_orders_count'), Order::where('status', OrderStatus::PENDING)->count()),
            Stat::make(__('panel.stats.in_processing_orders_count'),Order::whereIn('status', [OrderStatus::IN_PROCESSING,OrderStatus::PENDING_FOR_PAY])->count()),
            Stat::make(__('panel.stats.completed_orders_count'),Order::where('status', OrderStatus::COMPLETED)->count()),
            Stat::make(__('panel.stats.canceled_orders_count'), Order::where('status', OrderStatus::CANCELED)->count()),
            Stat::make(__('panel.stats.problematic_orders_count'), Order::where('status', OrderStatus::PROBLEMATIC)->count()),
            Stat::make(__('panel.stats.completed_orders_total'), Money::parse(Order::where('status', OrderStatus::COMPLETED)->sum("total"))->format()),
            Stat::make(__('panel.stats.workers_count'), Worker::count()),
            Stat::make(__('panel.stats.workers_requests_count'), FleetWorker::where('status','pending')->count()),
            Stat::make(__('forms.fields.app_commission'), Money::parse(Order::select(DB::raw('SUM(total - ((total/100)*(100-app_commission_percentage))) as app_commissions'))->first()->app_commissions)->format()),
        ];
    }


}
