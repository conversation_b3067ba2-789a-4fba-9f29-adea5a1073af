<?php

namespace Tasawk\Api\V1\Shared;

use DB;
use Tasawk\Api\Facade\Api;
use Tasawk\Http\Resources\Api\Shared\CategoryResource;
use Tasawk\Http\Resources\Api\Shared\CategoryWithChildrenResource;
use Tasawk\Http\Resources\Api\Shared\ServiceResource;
use Tasawk\Lib\Utils;
use Tasawk\Models\Category;

class CategoryServices {
    public function list() {
        $categories = Category::parent()
            ->enabled()
            ->orderBy(DB::raw("sort = 0, sort"))
            ->paginate();
        return Api::isOk(__("List of categories"), CategoryResource::collection($categories));
    }

    public function show(Category $category) {
        return Api::isOk(__("Category information"), CategoryResource::collection($category->children()->enabled()->latest()->paginate()));
    }

    public function bulk() {
        $categories = Category::whereIn('parent_id',request('ids'))
            ->enabled()->get();
        return Api::isOk(__("Category information"), CategoryResource::collection($categories));
    }

    public function services(Category $category) {
        return Api::isOk(__("Category information"), ServiceResource::collection($category->services()->enabled()->latest()->paginate()));

    }

}
