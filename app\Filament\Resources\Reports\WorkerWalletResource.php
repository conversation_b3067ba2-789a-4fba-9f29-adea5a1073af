<?php

namespace Tasawk\Filament\Resources\Reports;

use Filament\Infolists\Components\TextEntry;
use Filament\Infolists\Infolist;
use Filament\Resources\RelationManagers\RelationGroup;
use Filament\Tables\Columns\TextColumn;
use Illuminate\Support\Str;
use pxlrbt\FilamentExcel\Actions\Tables\ExportBulkAction;
use pxlrbt\FilamentExcel\Exports\ExcelExport;
use Tasawk\Filament\Resources\OrderResource;
use Tasawk\Filament\Resources\Reports\ProfitsResource\Pages;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;

use Tasawk\Filament\Resources\Reports\WorkerWalletResource\Pages\ListWorkerWallets;
use Tasawk\Filament\Resources\Reports\WorkerWalletResource\Pages\ViewWorkerWallet;
use Tasawk\Filament\Resources\Reports\WorkerWalletResource\RelationManagers\TransactionsManager;
use Tasawk\Models\Order;
use Tasawk\Models\User;
use Tasawk\Models\Worker;
use Tasawk\Traits\Filament\HasTranslationLabel;
use Theamostafa\Wallet\Models\Wallet;

class WorkerWalletResource extends Resource {
    use HasTranslationLabel;

    protected static ?string $model = Wallet::class;

    protected static ?string $navigationIcon = 'heroicon-o-wallet';

    public static function form(Form $form): Form {
        return $form
            ->schema([
                //
            ]);
    }

    public static function table(Table $table): Table {
        return $table
            ->modifyQueryUsing(fn($query) => $query->whereHas('holder.roles', fn($query) => $query->where('name', Worker::ROLE)))
            ->columns([

                TextColumn::make('id')->searchable(['id']),
                TextColumn::make('holder.name')->searchable(),
                TextColumn::make('balance')
                    ->summarize(Tables\Columns\Summarizers\Sum::make()->money('SAR')),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\ViewAction::make()
            ])
            ->bulkActions([

                Tables\Actions\BulkActionGroup::make([
                    ExportBulkAction::make()->exports([
                        ExcelExport::make("CSV")
                            ->fromTable()
                            ->withFilename(fn() => static::getPluralLabel() . '-' . now()->format('Y-m-d'))
                            ->withWriterType(\Maatwebsite\Excel\Excel::XLSX),


                    ]),
                ]),
            ]);
    }

    public static function infolist(Infolist $infolist): Infolist {
        return $infolist
            ->schema([
                TextEntry::make('id'),
                TextEntry::make('holder.name'),
                TextEntry::make('balance')->money('SAR'),
            ])->columns(1);
    }

    public static function getRelations(): array {
        return [
            RelationGroup::make(__('sections.transactions'), [
                TransactionsManager::make()
            ]),
        ];
    }

    public static function getPages(): array {
        return [
            'index' => ListWorkerWallets::route('/'),
            'view' => ViewWorkerWallet::route('/{record}/view'),
        ];
    }

    public static function getPluralLabel(): ?string {
        return __('menu.workers_wallets');
    }

    public static function getNavigationGroup(): ?string {
        return __('menu.reports');
    }

}
