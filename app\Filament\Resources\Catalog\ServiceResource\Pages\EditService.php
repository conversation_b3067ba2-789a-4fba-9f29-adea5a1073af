<?php

namespace Tasawk\Filament\Resources\Catalog\ServiceResource\Pages;

use Tasawk\Filament\Resources\Catalog\CategoryResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;
use Tasawk\Filament\Resources\Catalog\ServiceResource;

class EditService extends EditRecord {
    use EditRecord\Concerns\Translatable;

    protected static string $resource = ServiceResource::class;

    protected function getHeaderActions(): array {
        return [
            Actions\DeleteAction::make(),
            Actions\LocaleSwitcher::make(),

        ];
    }

    protected function getRedirectUrl(): string {
        return $this->getResource()::getUrl("index");
    }
}
