<?php

namespace Tasawk\Http\Resources\Api\Manager;

use Illuminate\Http\Resources\Json\JsonResource;
use Tasawk\Http\Resources\Api\Customer\CityResource;
use Tasawk\Http\Resources\Api\Shared\CategoryResource;
use Tasawk\Http\Resources\Api\Shared\SubCategoryResource;

class WorkerResource extends JsonResource {

    /**
     * Transform the resource into an array.
     *
     * @param \Illuminate\Http\Request $request
     * @return array
     */

    public function toArray($request) {
        $fleetAccount = $this->fleetAccount;
        $children = $fleetAccount?->categories()->with('children')->get()->pluck("children")->flatten()->each(function ($model) {
            $model->selected = in_array($model->id, $this->fleetAccount->categories->pluck("id")->toArray());
        });

        return [
            'id' => $this->id,
            'logo' => $fleetAccount->getFirstMediaUrl("logo"),
            'name' => $this->name,
            'email' => $this->email,
            'phone' => $this->phone,
            'api_token' => $this->api_token,
            'notification_status' => $this->settings['notification_status'] ?? 1,
            'preferred_language' => $this->settings['preferred_language'] ?? 'ar',
            "phone_verified" => (int)!is_null($this->phone_verified_at),
            'city' => CityResource::make($this->city),
            'location' => [
                'lat' => $fleetAccount?->location?->getCoordinates()[1],
                'lng' => $fleetAccount?->location?->getCoordinates()[0],
            ],

            'account_type' => $fleetAccount->account_type,
            'nationality' => $fleetAccount?->nationality,
            'residence_type' => $fleetAccount?->residence_type,

            'bank_account_number' => $fleetAccount?->bank_account,
            'categories' => CategoryResource::collection($fleetAccount->categories->where('parent_id', null)),
            'subcategories' => SubCategoryResource::collection($children),
            $this->mergeWhen($fleetAccount->nationality == 'other', [
                'job_title' => $fleetAccount?->job_title,
                'bondsman_name' => $fleetAccount?->bondsman_name,
                'bondsman_phone' => $fleetAccount?->bondsman_phone,
            ]),
            $this->mergeWhen($fleetAccount->residence_type == 'professional' && $fleetAccount->account_type != 'company' && $fleetAccount->nationality == 'other', [
                'organization_name' => $fleetAccount?->organization_name,
            ]),
            $this->mergeWhen($fleetAccount->account_type == 'company' || $fleetAccount->residence_type == 'professional' && $fleetAccount->nationality == 'other', [
                'commercial_registration_number' => $fleetAccount?->commercial_registration_number,
                'commercial_registration_image' => $fleetAccount?->getFirstMediaUrl('commercial_registration_image'),
            ]),

            'active' => $fleetAccount->active,
            'approve_letter_image' => $fleetAccount?->getFirstMediaUrl('approve_letter_image'),
            "can" => [
                'accept_orders' => $this->hasEnoughBalanceToAcceptOrders(),
            ]


        ];
    }
}
