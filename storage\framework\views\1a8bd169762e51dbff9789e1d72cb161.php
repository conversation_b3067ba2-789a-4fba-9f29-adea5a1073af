<?php if($faqs->count() > 0): ?>
    <section class="faq" id="faq-sec">
        <div class="container">
            <div class="titles-parent">
                <h4 class="sub-title animate__fadeInDown animate__animated wow"><?php echo e(trans('site.faqs')); ?> </h4>
               
                <h2 data-aos="fade-zoom-in" data-aos-easing="ease-in-back" data-aos-delay="200" data-aos-offset="0"
          class="general-title">
          <?php echo e(trans('site.faqs_desc')); ?>

        </h2>
            </div>
            <div class="questions-parent">
                <?php $__currentLoopData = $faqs; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $faq): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <div class="main-question ">
                        <div class="ques-details">
                            <div class="ques-num">
                                <?php echo e($loop->iteration); ?>

                            </div>
                            <h3 class="ques-text">
                                <?php echo e($faq->question); ?>

                            </h3>
                            <i class="fa-regular icon fa-plus"></i>
                        </div>
                        <div class="ques-answer">
                            <?php echo e($faq->answer); ?>

                        </div>
                    </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>
            <div class="readmore-holder">
            <a href="<?php echo e(route('page.faqs')); ?>" class="read-more"><?php echo e(trans('site.read_more')); ?> </a>
            </div>

        </div>
    </section>
<?php endif; ?>
<?php /**PATH D:\Workstation\hoomi\resources\views/site/includes/faqs.blade.php ENDPATH**/ ?>