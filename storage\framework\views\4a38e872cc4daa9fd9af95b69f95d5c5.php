<?php if(!is_null($features)): ?>
    <section class="features" id="features-sec">
        <div class="container">
            <div class="features-parent">
               
                    <img class="mobile-hand-img" data-aos-anchor=".list-item" data-aos-duration="400" data-aos-delay="0"
                    data-aos="fade-right" data-aos-easing="ease-out-cubic" src="<?php echo e(asset('storage/' . $feature_image)); ?>" alt="">
                <div class="features-cont">
                    <div class="titles-parent">
                        <h4 class="sub-title"><?php echo e(trans('site.some_features')); ?></h4>
                        <h2 class="general-title">
                            <?php echo e($our_features_description[app()->getLocale()]); ?>

                        </h2>
                    </div>
                    <div class="features-list">
                        <?php $__currentLoopData = $features; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $feature): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div class="list-item">
                                <div class="icon-circle">
                                    <i class="fa-regular icon fa-<?php echo e($feature['icon']); ?>"></i>
                                </div>
                                <h4 class="item-title"><?php echo e($feature['title'][app()->getLocale()]); ?></h4>
                            </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                </div>
            </div>
        </div>
    </section>
<?php endif; ?>
<?php /**PATH D:\Workstation\hoomi\resources\views/site/includes/features.blade.php ENDPATH**/ ?>