<?php

namespace Tasawk\Http\Requests\Api\Customer\Order;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;
use Tasawk\Lib\Utils;
use Tasawk\Rules\AddressBelongToAuthUserRule;
use Tasawk\Rules\IsAddressLocationInsideBranchBoundaries;
use Tasawk\Rules\IsDayDateInWorkingDateRule;
use Tasawk\Rules\IsProductAvailableInBranchRule;
use Tasawk\Rules\IsRequiredProductOptionsRepresentRule;
use Tasawk\Rules\IsValidProductOptionsRule;
use Tasawk\Rules\IsValidProductOptionValuesRule;
use Tasawk\Settings\GeneralSettings;


class UpdateOrderRequest extends FormRequest {

    public function authorize() {
        return true;
    }


    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules(): array {
        return [
            "receipt_method" => ['required', function ($attribute, $value, $fail) {
                if (!in_array($value, $this->route('order')->branch?->receipt_methods ?? [])) {
                    return $fail(__('panel.messages.not_support_receipt_method'));
                }
            }],
            'payment_gateway' => ['required', 'in:myfatoorah,cash'],
            'notes' => ['nullable'],

            // Support for updating services with quantities
            'services' => ['nullable', 'array', 'min:1', 'max:10'],
            'services.*.service_id' => ['required_with:services', 'exists:services,id'],
            'services.*.quantity' => ['required_with:services', 'integer', 'min:1', 'max:50'],
        ];
    }

}
