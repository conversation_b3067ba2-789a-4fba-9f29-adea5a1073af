<?php

namespace Tasawk\Http\Resources\Api\Customer\Products;

use Illuminate\Http\Resources\Json\JsonResource;

class LightProductResource extends JsonResource {


    public function toArray($request) {
        return [
            'id' => $this->id,
            'title' => $this->title,
            'image' => $this->getFirstMediaUrl(),
            'origin_price' => $this->price_include_taxes->format(),
            'sale_price' => $this->sale_price_include_taxes->format(),
            'final_price' => $this->full_price_include_taxes->format(),
            'available' => $this->isActive() ? ($this->branchInventory()?->isAvailable() ?? true) : false,
            'available_in_all_branches' => $this->isAvailableInAllBranches(),
            'has_options' => (boolean)$this->options()->count(),
            'has_options_in_current_selected_branch' => (boolean)$this->branchInventory()?->options()?->where('status',1)?->count()??false,
            'status' => $this->isActive(),
            'favorite' => (boolean)$request->user('sanctum')?->isFavorited($this),
        ];
    }
}
