<?php

namespace Tasawk\Http\Requests\Api\Manager;

use Illuminate\Foundation\Http\FormRequest;

use Illuminate\Validation\Rule;
use Illuminate\Validation\Rules\Enum;
use Str;
use Tasawk\Enum\OrderStatus;
use Tasawk\Rules\IsValidVerificationCodeRule;
use Tasawk\Rules\KSAPhoneRule;
use Tasawk\Rules\ManagerPhoneExistRule;

class RequestBalanceRequest extends FormRequest {

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize() {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules() {
        $userBalance = auth()->user()->balance;
        return [
            'amount' => ['required', 'numeric', "between:1,$userBalance"],
        ];
    }

}
