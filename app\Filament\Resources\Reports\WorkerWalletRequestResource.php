<?php

namespace Tasawk\Filament\Resources\Reports;

use Filament\Forms\Components\Hidden;
use Filament\Forms\Components\SpatieMediaLibraryFileUpload;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Infolists\Components\TextEntry;
use Filament\Infolists\Infolist;
use Filament\Resources\RelationManagers\RelationGroup;
use Filament\Tables\Columns\TextColumn;
use Illuminate\Support\Str;
use pxlrbt\FilamentExcel\Actions\Tables\ExportBulkAction;
use pxlrbt\FilamentExcel\Exports\ExcelExport;
use Tasawk\Enum\BalanceRequestStatusEnum;
use Tasawk\Filament\Resources\OrderResource;
use Tasawk\Filament\Resources\Reports\ProfitsResource\Pages;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;

use Tasawk\Filament\Resources\Reports\WorkerWalletRequestResource\Pages\ListWorkerWalletRequests;
use Tasawk\Filament\Resources\Reports\WorkerWalletRequestResource\Pages\ViewWorkerWalletRequest;
use Tasawk\Filament\Resources\Reports\WorkerWalletResource\Pages\ListWorkerWallets;
use Tasawk\Filament\Resources\Reports\WorkerWalletResource\Pages\ViewWorkerWallet;
use Tasawk\Filament\Resources\Reports\WorkerWalletResource\RelationManagers\TransactionsManager;
use Tasawk\Models\BalanceRequest;
use Tasawk\Models\Order;
use Tasawk\Models\User;
use Tasawk\Models\Worker;
use Tasawk\Traits\Filament\HasTranslationLabel;
use Theamostafa\Wallet\Models\Wallet;

class WorkerWalletRequestResource extends Resource {
    use HasTranslationLabel;

    protected static ?string $model = BalanceRequest::class;

    protected static ?string $navigationIcon = 'heroicon-o-wallet';

    public static function form(Form $form): Form {
        return $form
            ->schema([
                //
            ]);
    }

    public static function table(Table $table): Table {
        return $table
            ->modifyQueryUsing(fn($query) => $query->whereHas('user.roles', fn($query) => $query->where('name', Worker::ROLE)))
            ->columns([

                TextColumn::make('id')->searchable(['id']),
                TextColumn::make('user.name')
                    ->label(__("forms.fields.worker_name"))
                    ->searchable(),
                TextColumn::make('user.phone')
                    ->label(__("forms.fields.worker_phone"))
                    ->searchable(),
                TextColumn::make('user.fleetAccount.bank_account')
                    ->label(__("forms.fields.bank_account"))
                    ->copyable(),
                TextColumn::make('status')
                    ->color(fn($record) => $record->status->getColor())
                    ->badge(),
                TextColumn::make('amount')->label(__("forms.fields.request_amount"))->money('SAR'),
                TextColumn::make('user.balance')->money('SAR'),
                TextColumn::make('created_at')->date()->searchable(),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\Action::make('accept')
                    ->icon('heroicon-o-check-circle')
                    ->color('success')
                    ->label(__('forms.actions.accept'))
                    ->visible(fn($record) => $record->status == BalanceRequestStatusEnum::PENDING)
                    ->form([
                        TextInput::make('amount')
                            ->label(__('forms.fields.amount'))
                            ->formatStateUsing(fn($record) => $record->amount)
                            ->helperText(fn($record) => __("forms.fields.current_worker_balance_is", ['BALANCE' => $record->user->balance]))
                            ->suffix(__('forms.suffixes.sar'))
                            ->required(),
                        SpatieMediaLibraryFileUpload::make('receipt')
                            ->label(__('forms.fields.image'))
                            ->required()
                            ->columnSpanFull(),
                    ])->action(function ($record, $data) {
                        $record->update(['status' => BalanceRequestStatusEnum::COMPLETED]);
                        $record->user->withdraw($data['amount'], [
                            'description' => [
                                'ar' => __('panel.messages.admin_accept_balance_request_text', ['AMOUNT' => $data['amount']], 'ar'),
                                'en' => __('panel.messages.admin_accept_balance_request_text', ['AMOUNT' => $data['amount']], 'en')
                            ]
                        ]);
                    }),
                Tables\Actions\Action::make('reject')
                    ->color('danger')
                    ->icon('heroicon-o-x-circle')
                    ->label(__('forms.actions.reject'))
                    ->visible(fn($record) => $record->status == BalanceRequestStatusEnum::PENDING)
                    ->form([
                        Textarea::make('reason')
                            ->label(__('forms.fields.reason_id'))
                            ->required(),

                    ])->action(function ($record, $data) {
                        $record->update([
                            'status' => BalanceRequestStatusEnum::REJECTED,
                            'data' => ['reason' => $data['reason']]
                        ]);
                    }),
//                Tables\Actions\ViewAction::make()
            ])
            ->bulkActions([

                Tables\Actions\BulkActionGroup::make([
                    ExportBulkAction::make()->exports([
                        ExcelExport::make("CSV")
                            ->fromTable()
                            ->withFilename(fn() => static::getPluralLabel() . '-' . now()->format('Y-m-d'))
                            ->withWriterType(\Maatwebsite\Excel\Excel::XLSX),


                    ]),
                ]),
            ]);
    }

    public static function infolist(Infolist $infolist): Infolist {
        return $infolist
            ->schema([
                TextEntry::make('id'),
                TextEntry::make('user.name'),
                TextEntry::make('user.balance')->money('SAR'),
            ])->columns(1);
    }

    public static function getRelations(): array {
        return [
            RelationGroup::make(__('sections.transactions'), [
                TransactionsManager::make()
            ]),
        ];
    }

    public static function getPages(): array {
        return [
            'index' => ListWorkerWalletRequests::route('/'),
//            'view' => ViewWorkerWalletRequest::route('/{record}/view'),
        ];
    }

    public static function getPluralLabel(): ?string {
        return __('menu.workers_wallets_requests');
    }

    public static function getNavigationGroup(): ?string {
        return __('menu.reports');
    }

}
