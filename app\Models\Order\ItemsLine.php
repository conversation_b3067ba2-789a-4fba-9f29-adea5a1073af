<?php

namespace Tasawk\Models\Order;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;

class ItemsLine extends Model implements HasMedia {
    use HasFactory, InteractsWithMedia;

    protected $table = 'orders_items_lines';
    protected $casts = [
        'attributes' => 'array',
        'conditions' => 'array',
        'model' => 'array',
    ];
    protected $guarded = ['id'];
    public $timestamps = false;

    public function getCreatedAtColumn() {
    }


}
