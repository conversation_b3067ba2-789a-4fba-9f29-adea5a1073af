footer {
  padding-bottom: 55px;
}
.footer-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  .copyright {
    font-size: 14px;
    color: #121d32;
    font-weight: 400;
    text-align: center;
    margin-bottom: 10px;
  }
  .tasawk {
    font-size: 14px;
    color: #121d32;

    text-align: center;
    display: flex;
    align-items: center;
    a {
      margin-inline-start: 5px;
    }
  }
}

.footer-content {
  .main-links {
    display: flex;
    width: 100%;
    align-items: center;
    justify-content: center;
    column-gap: 35px;
    margin-bottom: 25px;
    a {
      font-size: 15px;
      text-transform: capitalize;
      color: #808080;
      text-align: center;
      transition: all ease-in-out 0.2s;
      &:hover {
        color: $orange-color;
      }
    }
  }
}

@include max-768 {
  footer {
    padding-bottom: 40px;
  }
  .footer-content .main-links {
    flex-wrap: wrap;
    row-gap: 20px;
    justify-content: center;
  }
}
