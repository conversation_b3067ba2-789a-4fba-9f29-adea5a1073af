<?php

namespace Tasawk\Http\Requests\Api\Customer;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;
use Tasawk\Rules\KSAPhoneRule;

class ContactUsRequest extends FormRequest {

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize() {
        return true;
    }

    public function rules() {
        $guest = Rule::requiredIf(fn() => auth()->guard('sanctum')->guest());
        return [
            "name" => [$guest, 'min:3'],
            "email" => [$guest, "email"],
            "phone" => [$guest, new KSAPhoneRule],
            "title" => ["required", 'min:3'],
            "message" => ["required", "min:25"],
            'contact_type_id' => 'required|exists:contact_types,id',
            'source' => ['nullable', 'in:client,worker'],
            'subject' => [],
        ];
    }

    protected function prepareForValidation() {
        $this->merge([
            "subject" => '',
        ]);
        if (!$this->filled("source")) {
            return $this->merge(['source' => 'client']);
        }
    }

}
