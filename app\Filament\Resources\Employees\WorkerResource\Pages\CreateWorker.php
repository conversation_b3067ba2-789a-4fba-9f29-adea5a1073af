<?php

namespace Tasawk\Filament\Resources\Employees\WorkerResource\Pages;

use Filament\Actions;
use Tasawk\Models\Worker;
use Tasawk\Models\WorkerDetiel;
use Filament\Resources\Pages\CreateRecord;
use Tasawk\Filament\Resources\Employees\WorkerResource;


class CreateWorker extends CreateRecord
{
    protected static string $resource = WorkerResource::class;



    protected $formData ;

    protected function getHeaderActions(): array {

        return [
      
        ];
    }
    protected function getRedirectUrl(): string {
        return $this->getResource()::getUrl("index");
    }

    protected function mutateFormDataBeforeCreate(array $data): array
    {

        $this->formData = $data;
        // dd($data['adddress']['lat']);
        // dd($data);
       
// dd($data);
    
        return $data;
    }

    // protected function afterCreate(): void {
    //     // dd($this->formData);
    //     WorkerDetiel::create([
    //         'available' => $this->formData['available'],
    //         'status' => $this->formData['status'],
    //         'zone_id' => $this->formData['status'],
    //         'city_id' => $this->formData['status'],
    //         'user_id' =>  $this->record->id,
    //         'latitude' =>  $this->formData['address']['lat'],
    //        'longitude' => $this->formData['address']['lng'],
    //        'address' => $this->formData['address']

    //     ]);
       

    // }
}
