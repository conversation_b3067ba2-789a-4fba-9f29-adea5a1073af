<?php

namespace Tasawk\Api\V1\Customer;

use DB;
use GeometryLibrary\SphericalUtil;
use Matan<PERSON>adaev\EloquentSpatial\Objects\Point;
use Str;
use Tasawk\Api\Core;
use Api;
use Tasawk\Http\Requests\Api\Customer\BranchesFilterRequest;
use Tasawk\Http\Requests\Api\Customer\ClosestBranchesBasedOnAddressRequest;
use Tasawk\Http\Requests\Api\Customer\ClosestBranchesBasedOnCoordinateRequest;
use Tasawk\Http\Resources\Api\Customer\Branches\LightBranchResource;
use Tasawk\Models\AddressBook;
use Tasawk\Models\Branch;

class BranchServices {

    public function index(BranchesFilterRequest $request): Core {

        return Api::isOk("Branches List",
            LightBranchResource::collection(static::getFilteredBranches())
        );
    }

    public function getClosestBasedOnAddress(ClosestBranchesBasedOnAddressRequest $request): Core {
        return Api::isOk("Branches List",
            LightBranchResource::collection(
                static::getClosetBasesOnAddress($request->input('address_id'))
            )
        );
    }

    public function getClosestBasedOnCoordinate(ClosestBranchesBasedOnCoordinateRequest $request): Core {

        return Api::isOk("Branches List",
            LightBranchResource::collection(
                static::getClosetBasesOnCoordinate($request->input('coordinate'))
            )
        );
    }

    static public function getFilteredBranches() {
        $query = Branch::canAcceptOrders();
        if (request()->string('coordinate')) {
            $query
                ->withDistanceSphere('location', new Point(...request()->string('coordinate')->explode(",")))
                ->orderBy('distance', 'asc');
        }

        return $query->get();
    }

    static public function getClosetBasesOnAddress(AddressBook $address) {
        [$lat, $lng] = [$address->map_location['lat'], $address->map_location['lng']];

        return self::getCloset($lat, $lng);

    }

    static public function getClosetBasesOnCoordinate($coordinate) {
        [$lat, $lng] = explode(',', Str::remove(" ", $coordinate));
        return self::getCloset($lat, $lng);
    }

    static public function getCloset($lat, $lng) {
        return Branch::canAcceptOrders()
            ->withDistanceSphere('location', new Point($lat, $lng))
            ->whereContains('boundaries', new Point($lat, $lng))
            ->where('maintenance_mode', 0)
            ->whereJsonContains('receipt_methods', 'delivery')
            ->get();

    }

    static public function getDistanceBetweenSelectedBranchAndUserCoordinate(Branch $branch, string $coordinate): float|int {
        [$lat, $lng] = explode(',', Str::remove(" ", $coordinate));
        return SphericalUtil::computeDistanceBetween(
            ['lat' => $branch->location->latitude, 'lng' => $branch->location->longitude],
            ['lat' => $lat, 'lng' => $lng],
        );


    }

}
