<?php

namespace Tasawk\Actions\Manager;


use Exception;
use Lorisle<PERSON>\Actions\Concerns\AsAction;
use Tasawk\Actions\Shared\Authentication\SendVerificationCode;
use Tasawk\Exceptions\AccountNeedActivationException;
use Tasawk\Exceptions\APIException;
use Tasawk\Models\Worker;

class WorkerHasRightsToLogin {
    use AsAction;


    /**
     * @throws Exception
     */
    public function handle() {
        $this->hasRoleManager()
            ->isPhoneVerified()
            ->inBlackList();
    }

    /**
     * @throws Exception
     */
    public function hasRoleManager(): static {
        if (!auth()->user()->hasRole(Worker::ROLE)) {
            throw new APIException(__('validation.api.invalid_credentials'));
        }
        return $this;
    }

    /**
     * @throws Exception
     */
    public function inBlackList(): static {
        if (!auth()->user()->active) {
            throw new APIException(__('validation.api.account_suspend'));
        }
        return $this;
    }

    /**
     * @throws Exception
     */
    /**
     * @throws Exception
     */
    public function isPhoneVerified(): static {
        if (is_null(auth()->user()->phone_verified_at)) {
            SendVerificationCode::run(auth()->user());
            throw new AccountNeedActivationException();
        }
        return $this;
    }

}
