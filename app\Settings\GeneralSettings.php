<?php

namespace Tasawk\Settings;

use <PERSON><PERSON>\LaravelSettings\Settings;

class GeneralSettings extends Settings {
    public string|null $app_logo;
    public string $app_name;
    public string $app_email;
    public string $app_phone;
    public string $app_whatsapp;
    public string $app_mobile;

    public string $app_address;
    public float $taxes;
    public string $app_commission;
    public int $minimum_worker_wallet_charge;
    public int $amount_of_minutes_to_cancel_order;
    public array $applications_links = [];
    public array $app_pages = [];

    public array $social_links = [];
    public array $working_days = [];
    public bool $show_subscriptions = false;

    public static function group(): string {
        return 'general';
    }
}