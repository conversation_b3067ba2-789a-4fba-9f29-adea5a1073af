<?php

namespace Tasawk\Http\Resources\Api\Customer\User;

use Carbon\Carbon;
use Illuminate\Http\Resources\Json\JsonResource;
use Payment;

class ReportResources extends JsonResource {

    /**
     * Transform the resource into an array.
     *
     * @param \Illuminate\Http\Request $request
     * @return array
     */
    public function toArray($request) {

        return [
            'id' => $this->id,
            'due_date' => Carbon::parse($this->payment_data['paid_at'])->timezone("africa/cairo")->format("Y-m-d H:i a"),
            'method'=>$this->payment_data['method'],
            'total' => $this->total->format(),
            'invoice_url' => route('orders.invoice.download', $this->id),

        ];
    }


}
