<?php

namespace Tasawk\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Tasawk\Models\Location\City;
use Tasawk\Models\Location\District;

class AddressBook extends Model {
    use HasFactory;

    protected $casts = [
        'map_location' => 'array',
        'primary' => 'boolean'
     ];

    protected $fillable = [
        "name",
        "user_id",
        "district_id",
        "phone",
        "map_location",
        "notes",
        "primary"
    ];

    protected static function booted() {
        parent::booted(); // TODO: Change the autogenerated stub
        static::creating(function ($builder) {
            if ($builder->primary) {
                static::where('user_id', $builder->user_id)->update(['primary' => 0]);
            }
        });
        static::updating(function ($builder) {
            if ($builder->primary) {
                static::where('user_id', $builder->user_id)->update(['primary' => 0]);
            }
        });
    }

    public function customer() {
        return $this->belongsTo(Customer::class, 'user_id');
    }

    public function district(): BelongsTo {
        return $this->belongsTo(District::class);
    }



}
