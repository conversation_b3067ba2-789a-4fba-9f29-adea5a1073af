<?php

namespace Tasawk\Filament\Resources\Employees\WorkerResource\Pages;

use Filament\Actions;
use Tasawk\Filament\Widgets\WorkersMap;
use Filament\Resources\Pages\ListRecords;
use Tasawk\Filament\Resources\Employees\WorkerResource;

class ListWorker extends ListRecords
{
    protected static string $resource = WorkerResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }

    protected function getHeaderWidgets(): array
    {
        return [
            // WorkersMap::class,
        ];
    }

}