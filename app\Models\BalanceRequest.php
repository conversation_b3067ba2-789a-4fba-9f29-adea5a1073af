<?php

namespace Tasawk\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;
use Tasawk\Enum\BalanceRequestStatusEnum;

class BalanceRequest extends Model implements HasMedia {
    use HasFactory,InteractsWithMedia;

    protected $guarded = [];
    protected $casts = [
        'data' => 'array',
        'status' => BalanceRequestStatusEnum::class,
    ];

    public function user() {
        return $this->belongsTo(User::class, 'user_id');
    }
}
