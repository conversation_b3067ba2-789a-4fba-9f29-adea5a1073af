<?php

namespace Tasawk\Filament\Resources\OrderResource\RelationManagers;

use Blade;
use Cknow\Money\Money;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Columns\SpatieMediaLibraryImageColumn;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Tasawk\Models\Order\ItemsLine;
use Tasawk\Settings\GeneralSettings;
use Tasawk\Traits\Filament\HasTranslationLabel;

class ItemsLineRelationManager extends RelationManager {
    protected static string $relationship = 'itemsLine';

    public function form(Form $form): Form {
        return $form
            ->schema([

            ]);
    }

    public function table(Table $table): Table {
        return $table
            ->heading(__('sections.products'))
            ->recordTitleAttribute('name')
            ->columns([
                Tables\Columns\TextColumn::make('name')->label(__('sections.products')),
                SpatieMediaLibraryImageColumn::make('model')
                    ->url(fn($record)=>$record->getFirstMediaUrl(),true)
                    ->label(__("forms.fields.bill_image"))
//                    ->defaultImageUrl('https://placehold.co/200x200')
                    ->defaultImageUrl(fn(GeneralSettings $settings)=>asset('storage/'. $settings->app_logo))

                    ->state(fn($record)=>!$record->getFirstMediaUrl()?__('forms.fields.no_image'):$record->getFirstMediaUrl())
                ,

                Tables\Columns\TextColumn::make('quantity'),
                Tables\Columns\TextColumn::make('price')
                    ->state(function (ItemsLine $record) {
                        return Money::parse($record->price)->format();
                    }),

                Tables\Columns\TextColumn::make('total')
                    ->state(function (ItemsLine $record) {
                        return Money::parse(($record->quantity * $record->price)+($record->quantity * collect($record->conditions)->sum('value')))->format();
                    }),

            ])->emptyStateDescription('')
            ->filters([
                //
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make(),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }


}
