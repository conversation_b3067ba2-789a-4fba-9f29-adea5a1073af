<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('fleet_workers', function (Blueprint $table) {
            $table->id();
            $table->string('account_type');
            $table->string('nationality');
            $table->string('residence_type');
            $table->foreignId('user_id');
            $table->point('location')->nullable();
            $table->longText('bank_account')->nullable();
            $table->string('commercial_registration_number')->nullable();
            $table->string('job_title')->nullable();
            $table->string('bondsman_name')->nullable();
            $table->string('bondsman_phone')->nullable();
            $table->string('organization_name')->nullable();

            $table->string('status');
            $table->timestamps();
        });
        Schema::create('fleet_worker_categories', function (Blueprint $table) {
            $table->foreignId('fleet_worker_id');
            $table->foreignId('category_id');

        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('fleet_worker_categories');
        Schema::dropIfExists('fleet_workers');
    }
};
