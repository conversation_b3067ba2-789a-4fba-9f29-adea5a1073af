<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Tasawk\Enum\ReasonForEnum;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void {
        Schema::create('report_reasons', function (Blueprint $table) {
            $table->id();
            $table->text('name');
            $table->string('for');
            $table->boolean('status');
            $table->timestamps();
        });
        Schema::create('orders_report', function (Blueprint $table) {
            $table->id();
            $table->foreignId("order_id");
            $table->foreignId("reason_id")->nullable();
            $table->text("note")->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void {
        Schema::dropIfExists('report_reasons');
        Schema::dropIfExists('orders_report');
    }
};
