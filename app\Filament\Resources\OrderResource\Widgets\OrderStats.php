<?php

namespace Tasawk\Filament\Resources\OrderResource\Widgets;

use BezhanSalleh\FilamentShield\Traits\HasWidgetShield;
use Cknow\Money\Money;
use DB;
use Filament\Widgets\Concerns\InteractsWithPageTable;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;
use Tasawk\Enum\OrderStatus;
use Tasawk\Filament\Resources\OrderResource\Pages\ListOrders;

class OrderStats extends BaseWidget {
    use HasWidgetShield;
    use InteractsWithPageTable;
    protected static ?int $sort = 1;
    protected static function getPermissionName(): string {
        return 'view_any_order';
    }

    protected function getStats(): array {

        return [

            Stat::make(__('panel.stats.new_orders_total'), Money::parse($this->getPageTableQuery()->where('status', OrderStatus::PENDING)->sum('total'))->format()),
            Stat::make(__('panel.stats.in_processing_orders_total'), Money::parse($this->getPageTableQuery()->whereIn('status', [OrderStatus::PENDING_FOR_PAY, OrderStatus::IN_PROCESSING,])->sum('total'))->format()),
            Stat::make(__('panel.stats.completed_orders_total'), Money::parse($this->getPageTableQuery()->where('status', OrderStatus::COMPLETED)->sum('total'))->format()),
            Stat::make(__('panel.stats.canceled_orders_total'), Money::parse($this->getPageTableQuery()->where('status', OrderStatus::CANCELED)->sum('total'))->format()),
            Stat::make(__('panel.stats.problematic_orders_total'), Money::parse($this->getPageTableQuery()->where('status', OrderStatus::PROBLEMATIC)->sum('total'))->format()),
        ];
    }


    protected function getTablePage(): string {
        return ListOrders::class;
    }
}
