<?php

namespace Tasawk\Models;

use Cknow\Money\Casts\MoneyDecimalCast;
use Cknow\Money\Money;
use Darryldecode\Cart\CartCondition;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;
use Tasawk\Enum\AccountTypeEnum;
use Tasawk\Enum\AllOrderStatus;
use Tasawk\Enum\OrderPaymentStatus;
use Tasawk\Enum\OrderStatus;
use Tasawk\Enum\OrderTypeEnum;
use Tasawk\Enum\ReceiptMethods;
use Tasawk\Lib\ArrayStorage;
use Tasawk\Lib\Cart as CoreCart;
use Tasawk\Lib\Utils;
use Tasawk\Models\Order\Condition;
use Tasawk\Models\Order\ItemsLine;
use Tasawk\Models\Order\Report;
use Tasawk\Models\OrderService;
use Tasawk\Notifications\Order\OrderAcceptedNotification;
use Tasawk\Notifications\Order\OrderStatusChangedNotification;
use Tasawk\Settings\GeneralSettings;

class Order extends Model implements HasMedia {
    use InteractsWithMedia;

    protected $fillable = [

        'customer_id',
        'address_id',
        'date',
        'time',
        'category_id',
        'sub_category_id',
        'order_type',
        'worker_id',
        'worker_type',
        'payment_status',
        'payment_data',
        'status',
        'total',
        'notes',
        'app_commission_percentage',
        'service_id'
    ];
    protected $casts = [
        'total' => MoneyDecimalCast::class,
        'date' => 'date',
        'time' => 'datetime',
        'payment_data' => 'array',
        'status' => OrderStatus::class,
        'payment_status' => OrderPaymentStatus::class,
        'order_type' => OrderTypeEnum::class,
        'worker_type' => AccountTypeEnum::class
    ];

    protected static function booted() {
        parent::booted(); // TODO: Change the autogenerated stub
        static::updating(function (Order $order) {
            if ($order->getOriginal('status') != $order->status) {
//                if ($order->status === OrderStatus::PENDING->value) {
//                    Notification::send($order->customer, new OrderAcceptedNotification($order));
//                }
                try {

                    \Notification::send([$order->customer, ... Utils::getAdministrationUsers()], new OrderStatusChangedNotification($order));
                } catch (\Exception $e) {

                }
            }
        });
    }

    public function scopeFiltered($builder) {

        return $builder
            ->when(request()->get('status') == 'new', fn($builder) => $builder->whereNull('worker_id')->whereHas('address.district', fn($q) => $q->where("city_id", auth()->user()->city_id))->where('status', OrderStatus::PENDING))
            ->when(request()->get('status') == OrderStatus::IN_PROCESSING->value, fn($builder) => $builder->where('worker_id', auth()->id())->whereIn('status', [OrderStatus::IN_PROCESSING, OrderStatus::PROBLEMATIC->value, OrderStatus::PENDING_FOR_PAY]))
            ->when(request()->get('status') == OrderStatus::COMPLETED->value, fn($builder) => $builder->where('worker_id', auth()->id())->whereIn('status', [OrderStatus::COMPLETED, OrderStatus::CANCELED]));
    }

    public function getOrderNumberAttribute(): string {
        return sprintf("%'.06d", $this->id);
    }

    public function worker() {
        return $this->belongsTo(Worker::class);
    }

    public function scopePaid($builder) {
        return $builder->where('payment_status', OrderPaymentStatus::PAID)->orWhere("payment_data->method", 'cash');
    }

    function itemsLine(): HasMany {
        return $this->hasMany(ItemsLine::class);
    }

    function conditions(): HasMany {
        return $this->hasMany(Condition::class);
    }


    public function getAsCartAttribute() {
        $eventsClass = config('shopping_cart.events');
        $events = $eventsClass ? new $eventsClass() : app('events');
        $session_key = md5($this->cart_id . \Str::random());
        $instanceName = $session_key . 'back_end_order_cart';
        $cart = new CoreCart(
            new ArrayStorage,
            $events,
            $instanceName,
            $session_key,
            config('shopping_cart')
        );

        $this->itemsLine->transform(function (ItemsLine $item) {
            $conditions = collect($item->conditions)->map(fn($cond) => new CartCondition($cond))->toArray();
            $item['quantity'] = $item->quantity > 0 ? $item->quantity : 1;
            $item['associatedModel'] = [
                ...$item->model,
                'image' => $item->getFirstMediaUrl()
            ];
            $item['new_conditions'] = $conditions;


            return $item;
        })->each(function ($item) use ($cart) {
            $d = $item->toArray();

            $d['conditions'] = $d['new_conditions'];
            return $cart->add($d);
        });
        $this->conditions->each(fn($condition) => $cart->condition(new CartCondition($condition->toArray())));

        return $cart;
    }

    public function address(): BelongsTo {
        return $this->belongsTo(AddressBook::class);
    }

    public function customer() {
        return $this->belongsTo(Customer::class);
    }

    public function rate() {
        return $this->hasOne(OrderRate::class);
    }

    public function rated() {
        return $this->rate()->exists();
    }

    public function branch() {
        return $this->belongsTo(Branch::class);
    }

    public function cancellation(): HasOne {
        return $this->hasOne(OrderCancellation::class, "order_id");
    }

    public function cancel($reason, $note) {
        $this->update(['status' => OrderStatus::CANCELED]);
        return $this->cancellation()->create(['cancellation_reason_id' => $reason, 'note' => $note]);
    }

    public function getAvailableStatus() {
        return collect(OrderStatus::cases())->map(fn($status) => $status->value)->map(function ($status) {
            return [
                'value' => $status,
                'label' => __("panel.enums." . $status)
            ];
        })
            ->values();

    }

    public function canRate(): bool {
        return !$this->rate()->exists() && $this->status == OrderStatus::COMPLETED;
    }

    public function getRenderStatus() {


        if ($this->status == OrderStatus::CANCELED) {
            return $this->status->getLabel() . ' - ' . $this->cancellation?->getReason();
        }
        return $this->status->getLabel();
    }

    public function category() {
        return $this->belongsTo(Category::class);
    }

    public function subCategory(): BelongsTo {
        return $this->belongsTo(Category::class, 'sub_category_id');
    }

    public function workerType() {
        return $this->belongsTo(WorkerType::class, 'worker_type_id');
    }

    public function canReport(): bool {
        return !$this->report()->exists() && !in_array($this->status, [OrderStatus::COMPLETED, OrderStatus::PROBLEMATIC, OrderStatus::CANCELED]);;
    }

    public function canCancel(): bool {
        $minutes = (new GeneralSettings())->amount_of_minutes_to_cancel_order;

        if ($this->created_at->diffInMinutes() > $minutes) {
            return false;
        }
        return !$this->cancellation()->exists() && in_array($this->status, [OrderStatus::PENDING, OrderStatus::IN_PROCESSING]);
    }

    public function canSetPrice(): bool {
        return $this->status == OrderStatus::IN_PROCESSING;
    }

    public function report(): HasOne {
        return $this->hasOne(Report::class);
    }

    public function getAppCommissionAttribute(): float|int {
        $total = Money::parse($this->as_cart->totals()['subtotal'])->formatByDecimal();

        return ($total / 100) * $this->app_commission_percentage;
    }

    public function getWorkerDuesAttribute(): float|int {
        $total = Money::parse($this->as_cart->totals()['subtotal'])->formatByDecimal();
        return $total - $this->app_commission;
    }

    public function canAccept(): bool {
        return $this->status == OrderStatus::PENDING;
    }

    public function canPay(): bool {
        return $this->status == OrderStatus::PENDING_FOR_PAY;
    }

    public function workers(): BelongsToMany {
        return $this->belongsToMany(Worker::class, 'order_worker');
    }

    public function service() {
        return $this->belongsTo(Service::class, 'service_id')->withoutGlobalScopes();
    }

    public function orderServices(): HasMany {
        return $this->hasMany(OrderService::class);
    }

    public function services(): BelongsToMany {
        return $this->belongsToMany(Service::class, 'order_services')
                    ->withPivot(['quantity', 'unit_price', 'total_price'])
                    ->withTimestamps();
    }

}
