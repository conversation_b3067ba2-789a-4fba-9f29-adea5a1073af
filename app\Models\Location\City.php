<?php

namespace Tasawk\Models\Location;

use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Matan<PERSON>adaev\EloquentSpatial\Objects\Geometry;
use MatanYadaev\EloquentSpatial\Objects\Polygon;
use Spatie\Translatable\HasTranslations;
use Tasawk\Models\Occasions\Occasion;
use Tasawk\Traits\Publishable;
use Tasawk\Models\Customer;
use Tasawk\Models\Zone;


class City extends Model {
    use HasFactory, HasTranslations, Publishable;

    public array $translatable = ['name'];
    protected $casts = [
        'name' => 'json',
        'boundaries' => Polygon::class
    ];


    protected $fillable = [
        'id',
        'name',
        'zone_id',
        'status',
        'boundaries'

    ];

    protected function boundaries(): Attribute {

        return Attribute::make(

            set: function ($value) {
                $this->refresh();

                return Geometry::fromJson($value)->toSqlExpression($this->getConnection());
            }
        );
    }

    public function districts(): HasMany {
        return $this->hasMany(District::class);
    }

    public function customers(): HasMany {
        return $this->hasMany(Customer::class, 'city_id');
    }


    public function zone(): BelongsTo {
        return $this->belongsTo(Zone::class);
    }


}
