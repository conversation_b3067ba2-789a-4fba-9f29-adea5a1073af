<?php

namespace Tasawk\Actions\Shared\Authentication;

use Lorisleiva\Actions\Concerns\AsAction;
use Tasawk\Lib\NotificationMessageParser;
use Tasawk\Lib\SMS;
use Tasawk\Lib\Utils;
use Tasawk\Models\VerificationCode;
use Tasawk\Notifications\OTPCodeSentNotification;

class SendVerificationCode {
    use AsAction;

    public function handle($user = null, $phone = null) {
        $code = Utils::randomOtpCode();
        VerificationCode::create(['phone' => $phone ?? $user->phone, "code" => $code, 'user_id' => $user?->id]);
        SMS::init($phone ?? $user->phone, "Hoomi - Your OTP Code $code")->send();
    }

}
