<?php

namespace Tasawk\Enum;

use Filament\Support\Contracts\HasLabel;

enum OrderTypeEnum: string implements HasLabel {
    case IMMEDIATELY = 'immediately';
    case NORMAL = 'normal';
    public function getLabel(): ?string {
        return __("panel.enums.$this->value");
    }

    public function getColor(): string {
        return match ($this->value) {
            'normal', => 'warning',
            default=>'danger'
        };

    }

}
