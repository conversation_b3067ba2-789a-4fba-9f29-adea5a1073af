//  common questiona section
.faq {
  padding: 150px 0;
  .title-text {
    color: $green-color;
  }
  .sub-title {
    margin-bottom: 35px;
  }
  .titles-parent {
    margin-bottom: 55px;
  }
}
.ques-details {
  display: flex;
  align-items: center;
}
.main-question {
  position: relative;
  margin-bottom: 2px;
  border-bottom: 1px solid #e6e6e6;
  padding: 30px 0;
  cursor: pointer;
}
.main-question:first-of-type {
  border-top: 1px solid #e6e6e6;
}
.main-question.active {
  .ques-text,
  .ques-details .icon,
  .ques-num {
    color: $orange-color;
  }
  .ques-details .icon {
    transform: rotate(180deg);
  }
  .fa-plus:before {
    content: "\f068";
  }
}

.ques-num {
  color: #cccccc;
  transition: all ease-in-out 0.1s;
  font-size: 20px;
  width: 25px;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  flex-shrink: 0;
}

.ques-text {
  font-size: 18px;
  transition: all ease-in-out 0.1s;
  line-height: 34px;
  color: #414042;
  font-weight: 700;
  margin: 0;
  margin-inline-start: 25px;
  cursor: pointer;
  max-width: 85%;
}

.ques-details {
  .icon {
    font-size: 24px;
    display: flex;
    margin-inline-start: auto;
    align-items: center;
    justify-content: center;
    color: #cccccc;
    transition: all linear 0.2s;
  }
}
.ques-answer {
  color: #010101;
  width: 839px;
  padding-inline-start: 50px;
  font-size: 16px;
  line-height: 32px;
  text-align: justify;
  margin-top: 12px;
  display: none;
  font-weight: 400;
}

.active-color-blue {
  color: #18778d !important;
}

.questions-parent {
  max-width: 950px;
  margin-left: auto;
  margin-right: auto;
}

@include max-1200 {
  .questions-parent,
  .ques-answer {
    width: 100%;
  }
  .ques-answer {
    padding-inline-end: 50px;
  }
}
@include max-992 {
  .ques-text {
    font-size: 16px;
  }
}

@include max-768 {
  .main-question {
    padding: 20px 0;
  }
  .ques-text {
    line-height: 1.8;
  }
  .faq .sub-title {
    margin-bottom: 20px;
  }
  .faq .titles-parent {
    margin-bottom: 30px;
  }
  .ques-text {
    font-size: 14px;
    margin-inline-start: 12px;
    padding-inline-end: 15px;
  }
  .faq {
    padding: 50px 0;
  }
  .ques-answer {
    font-size: 14px;
    line-height: 1.9;
    padding-inline-start: 34px;
  }
  .ques-answer {
    padding-inline-end: 40px;
  }
  .ques-num {
    width: 22px;
    font-size: 19px;
  }
}
