.contact {
  padding-top: 115px;
  padding-bottom: 65px;
  .contact-content {
    display: grid;
    grid-template-columns: 750px 1fr;
    column-gap: 30px;
  }
  .titles-parent {
    margin-bottom: 55px;
    .general-title {
      margin-bottom: 0;
    }
    .sub-title {
      margin-bottom: 25px;
    }
  }
}

.form-div {
  padding-top: 40px;
  padding-bottom: 50px;
  padding-left: 30px;
  padding-right: 30px;
  border-radius: 15px;
  background-color: #f2f2f2;

  .text {
    font-size: 18px;
    color: #000;
    font-weight: 500;
    text-align: start;
    margin-bottom: 25px;
  }
}
.myform {
  .inputs {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 20px 10px;
    .select2-container[dir="rtl"]
      .select2-selection--single
      .select2-selection__rendered {
      padding: 0;
    }
    .select2-container--default
      .select2-selection--single
      .select2-selection__rendered {
      font-size: 14px;
      color: #808080;
    }
    input,
    .select2-selection--single,
    textarea {
      padding-inline-start: 30px;
      padding-inline-end: 20px;
      width: 340px;
      height: 50px;
      position: relative;
      border-radius: 25px;
      background-color: #ffffff;
      border: 2px solid #ebebeb;
      transition: all ease-in-out 0.2s;
      &:focus {
        outline: none !important;
        border-color: $orange-color;
      }
      &::placeholder {
        font-size: 14px;
        color: #808080;
        font-weight: 400;
      }
    }
    textarea {
      padding-top: 20px;
      grid-column: 1 / 3;
      display: block;
      width: 100%;
      height: 130px;
      resize: none;
      padding-bottom: 10px;
    }
    .select2-container--default .select2-selection--single {
      border-color: #ebebeb;
      outline: none;
      &:focus {
        .select2-selection__arrow::after {
          transform: translateY(-50%) rotate(180deg);
        }
        border-color: $orange-color !important;
      }
    }
  }
  .select2-container--default
    .select2-selection--single
    .select2-selection__arrow {
    top: 50%;
    transform: translateY(-50%);
    inset-inline-end: 15px;
    &::after {
      position: absolute;
      content: "\f078";
      font-family: "Font Awesome 6 Pro";
      font-size: 14px;
      color: #808080;
      top: 50%;
      transform: translateY(-50%);
      transition: all ease-in-out 0.2s;
    }
  }
  .select2-container--default
    .select2-selection--single
    .select2-selection__arrow
    b {
    display: none !important;
  }
  .select2-container--default
    .select2-selection--single
    .select2-selection__rendered {
    height: 100%;
    display: flex;
    align-items: center;
  }
  .submit-holder {
    display: flex;
    justify-content: flex-end;
    margin-top: 15px;
    button {
      width: 150px;
      height: 50px;
      border-radius: 25px;
      background-color: $orange-color;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 18px;
      color: #ffffff;
      font-weight: 700;
      text-align: center;
      text-transform: capitalize;
      transition: all ease-in-out 0.2s;
      &:hover {
        background-color: $green-color;
        color: #fff;
      }
    }
  }
}
.phone-val {
  direction: ltr;
}

.social-div {
  padding-inline-start: 35px;
  padding-inline-end: 20px;
  padding-bottom: 50px;
  padding-top: 40px;
  border-radius: 15px;
  background-color: $green-color;
  .head {
    font-size: 18px;
    color: #ffffff;
    font-weight: 500;
    margin-bottom: 25px;
    line-height: normal;
  }
}
.social-items {
  .item {
    display: flex;
    align-items: flex-start;
    flex-direction: column;
    margin-bottom: 20px;
    .text-icon {
      margin-bottom: 12px;
      align-items: center;
    }
    .text {
      font-size: 14px;
      color: #ffffff;
      font-weight: 500;
      margin-inline-start: 10px;
      display: inline-block;
      text-transform: capitalize;
    }
    .icon {
      font-size: 16px;
      color: #fff;
    }
    .item-val {
      font-size: 14px;
      color: #ffffff;
      font-weight: 400;
      transition: all ease-in-out 0.2s;
      &:hover {
        color: $orange-color;
      }
    }
  }
}
.social-icons {
  display: flex;
  align-items: center;
  column-gap: 10px;
  margin-top: 25px;
  a {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #ffffff;
    transition: all ease-in-out 0.2s;
    &:hover {
      background-color: $orange-color;
      i {
        color: #fff;
      }
    }
    i {
      font-size: 18px;
      color: $green-color;
    }
  }
}

@include max-1200 {
  .contact .contact-content {
    grid-template-columns: 635px 1fr;
  }
  .myform .inputs input,
  .myform .inputs .select2-container--default .select2-selection--single,
  .myform .inputs textarea,
  .select2-container {
    width: 100% !important;
  }
}

@include max-992 {
  .contact .contact-content {
    grid-template-columns: 1fr;
    row-gap: 30px;
  }
  .social-div {
    width: 360px;
    margin-left: auto;
    margin-right: auto;
  }
}

@include max-768 {
  .myform .inputs {
    grid-template-columns: 1fr;
  }
  .myform .inputs textarea {
    grid-column: unset;
  }
  .contact .titles-parent {
    margin-bottom: 30px;
  }
  .contact {
    padding: 50px 0;
  }
  .form-div .text {
    font-size: 16px;
    text-align: center;
    line-height: 1.7;
  }
  .form-div {
    padding-left: 20px;
    padding-right: 20px;
    padding-top: 30px;
    padding-bottom: 40px;
  }
  .myform .submit-holder {
    justify-content: center;
  }
  .myform .submit-holder button {
    width: 100%;
  }
  .myform .inputs {
    input,
    textarea,
    .select2-selection--single {
      padding-inline-start: 20px;
    }
  }
  .contact .titles-parent .sub-title {
    margin-bottom: 20px;
  }
  .myform .submit-holder {
    margin-top: 20px;
  }
}

@media screen and (max-width: 400px) {
  .social-div {
    width: 100%;
  }
}
