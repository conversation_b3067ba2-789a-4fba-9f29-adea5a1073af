<?php

namespace Tasawk\Enum;

use Filament\Support\Contracts\HasLabel;

enum BalanceRequestStatusEnum: string implements HasLabel {
    case PENDING = 'pending';
    case COMPLETED = 'completed';
    case REJECTED = 'rejected';

    public function getLabel(): ?string {
        return __("panel.enums.$this->value");

    }
    public function getColor(): string {
        return match ($this->value) {
            'pending' => 'warning',
            'completed' => 'success',
            'rejected' => 'danger',
        };
    }
}
