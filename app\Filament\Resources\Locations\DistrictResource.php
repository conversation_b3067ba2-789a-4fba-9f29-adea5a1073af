<?php

namespace Tasawk\Filament\Resources\Locations;

use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Form;
use Filament\Resources\Concerns\Translatable;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Actions\Action;
use Filament\Tables\Columns\IconColumn;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Model;

use Tasawk\Filament\Resources\Locations\DistrictResource\Pages\ListDistricts;
use Tasawk\Models\Location\City;
use Tasawk\Models\Location\District;
use Tasawk\Traits\Filament\HasTranslationLabel;

class DistrictResource extends Resource
{
    use Translatable, HasTranslationLabel;
    protected static ?string $model = District::class;
    protected static ?int $navigationSort = 3;

    protected static ?string $navigationIcon = 'heroicon-o-map-pin';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                TextInput::make('name')->required()->columnSpanFull(),
                Select::make('city_id')
                      ->relationship('city', 'name')
                      ->searchable()
                    ->preload()
                    ->columnSpanFull()
                    ->getOptionLabelFromRecordUsing(fn (Model $record) => "{$record->name}")
                    ->required(),
                Toggle::make('status')->default(1)
                    ->onColor('success')
                    ->offColor('danger'),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('id'),
                TextColumn::make('name'),
                TextColumn::make('city.name')->label(__('forms.fields.city_id')),
                IconColumn::make('status')
                    ->boolean()
                    ->action(
                        Action::make('Active')
                            ->label(fn(District $record): string => $record->status ? __('panel.messages.deactivate') : __('panel.messages.activate'))
                            ->requiresConfirmation()
                            ->action(fn(District $record) => $record->toggleStatus())


                    )
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make()
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getNavigationGroup(): ?string {
        return __('menu.locations');
    }


    public static function getPages(): array
    {
        return [
            'index' => ListDistricts::route('/'),
            'create' => \Tasawk\Filament\Resources\Locations\DistrictResource\Pages\CreateDistrict::route('/create'),
            'edit' => \Tasawk\Filament\Resources\Locations\DistrictResource\Pages\EditDistrict::route('/{record}/edit'),
        ];
    }
}
