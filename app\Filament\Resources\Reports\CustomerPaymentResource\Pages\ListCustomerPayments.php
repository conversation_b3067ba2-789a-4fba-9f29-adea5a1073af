<?php

namespace Tasawk\Filament\Resources\Reports\CustomerPaymentResource\Pages;

use Tasawk\Filament\Resources\Reports\CustomerPaymentResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListCustomerPayments extends ListRecords
{
    protected static string $resource = CustomerPaymentResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
