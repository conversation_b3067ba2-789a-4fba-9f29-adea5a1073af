<?php

namespace Tasawk\Http\Resources\Api\Manager\Orders;

use Illuminate\Http\Resources\Json\JsonResource;

class CustomerResource extends JsonResource {

    /**
     * Transform the resource into an array.
     *
     * @param \Illuminate\Http\Request $request
     * @return array
     */

    public function toArray($request) {
        return [
            'id' => $this->id,
            'avatar' => $this->getFirstMediaUrl(),
            'name' => $this->name,
            'email' => $this->email,
            'phone' => $this->phone,
            'distance'=>$request->get('distance')
        ];
    }
}
