<?php

namespace Tasawk\Filament\Resources\Locations;

use Cheesegrits\FilamentGoogleMaps\Fields\Map;
use Closure;
use Filament\Forms\Components\Hidden;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Form;
use Filament\Notifications\Notification;
use Filament\Resources\Concerns\Translatable;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Actions\Action;
use Filament\Tables\Columns\IconColumn;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Str;
use MatanYadaev\EloquentSpatial\Objects\Geometry;
use Tasawk\Filament\Resources\Locations\CityResource\Pages\CreateCity;
use Tasawk\Filament\Resources\Locations\CityResource\Pages\EditCity;
use Tasawk\Filament\Resources\Locations\CityResource\Pages\ListCities;
use Tasawk\Models\Location\City;
use Tasawk\Models\Zone;
use Tasawk\Traits\Filament\HasTranslationLabel;

class CityResource extends Resource {
    use HasTranslationLabel, Translatable;

    protected static ?string $model = City::class;

    protected static ?int $navigationSort = 2;
    protected static ?string $navigationIcon = 'heroicon-o-map-pin';

    public static function form(Form $form): Form {
        return $form
            ->schema([
                TextInput::make('name')->required()->columnSpanFull(),
                Map::make("location")
                    ->label(__("forms.fields.address"))
                    ->defaultLocation([24.7136, 46.6753])
                    ->defaultZoom(12)
                    ->draggable()
                    ->required()
                    ->rules([
                        function ($get) {
                            return function (string $attribute, $value, Closure $fail) use ($get) {

                                if (!$get('boundaries')) {
                                    $fail(__('validation.boundaries_required', ['attribute' => __('forms.fields.boundaries')],));
                                }
                            };
                        },
                    ])
                    ->drawingControl()
                    ->drawingField('boundaries')
                    ->drawingModes(fn($get) => $get('boundaries') ? [] : ['marker' => false, 'circle' => false, 'polygon' => true, 'polyline' => false, 'rectangle' => false])
                    ->clickable()

                    ->required()
                    ->geolocate(),

                Hidden::make('boundaries')
                    ->formatStateUsing(function ($record) {
                        if (!$record)
                            return;
                        $json = json_decode(Geometry::fromJson($record?->boundaries?->toJson())->toFeatureCollectionJson(), true);
                        $json['features'][0]['properties'] = [
                            'id' => Str::uuid(),
                            'type' => 'polygon'
                        ];
                        return json_encode($json, JSON_NUMERIC_CHECK);
                    }),
                Toggle::make('status')->default(1)
                    ->onColor('success')
                    ->offColor('danger'),

            ])->columns(1);
    }

    public static function table(Table $table): Table {
        return $table
            ->columns([
                TextColumn::make('id'),
                TextColumn::make('name'),
                IconColumn::make('status')
                    ->boolean()
                    ->action(
                        Action::make('Active')
                            ->label(fn(City $record): string => $record->status ? __('panel.messages.deactivate') : __('panel.messages.activate'))
                            ->requiresConfirmation()
                            ->action(fn(City $record) => $record->toggleStatus())


                    )
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make()
                    ->before(function (Tables\Actions\DeleteAction $action, City $City) {
                        if ($City->distracts()->count() > 0) {
                            Notification::make()
                                ->warning()
                                ->title(__('panel.messages.warning'))
                                ->body(__('panel.messages.city_delete', ['City' => $City->name]))
                                ->persistent()
                                ->send();
                            $action->cancel();
                        }
                    }),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array {
        return [
            //
        ];
    }


    public static function getNavigationGroup(): ?string {
        return __('menu.locations');
    }

    public static function getPages(): array {
        return [
            'index' => ListCities::route('/'),
            'create' => CreateCity::route('/create'),
            'edit' => EditCity::route('/{record}/edit'),
        ];
    }
}
