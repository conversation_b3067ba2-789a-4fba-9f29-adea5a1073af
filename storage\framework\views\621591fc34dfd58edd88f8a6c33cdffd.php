<section class="contact" id="contact-sec">
    <div class="container">
        <div class="titles-parent">
            <h4 class="sub-title wow animate__fadeInDown animate__animated"><?php echo e(trans('site.contact_us')); ?> </h4>

            <h2 class="general-title" data-aos="fade-down" data-aos-easing=" ease-in" data-aos-duration="600">
                <?php echo e(trans('site.contact_desc')); ?>

            </h2>
        </div>
        <div class="contact-content">
            <div class="form-div ">
                <p class="text">
                    <?php echo e(trans('site.contact_header')); ?>

                </p>
                <?php
$__split = function ($name, $params = []) {
    return [$name, $params];
};
[$__name, $__params] = $__split('contact-us', []);

$__html = app('livewire')->mount($__name, $__params, 'lw-983956019-0', $__slots ?? [], get_defined_vars());

echo $__html;

unset($__html);
unset($__name);
unset($__params);
unset($__split);
if (isset($__slots)) unset($__slots);
?>
            </div>
            <div class="social-div ">

                <h3 class="head"><?php echo e(trans('site.contact_info')); ?> </h3>
                <div class="social-items">
                    
                    <div class="item">
                        <div class="d-flex text-icon">
                            <i class="fa-regular icon fa-mobile"></i>
                            <span class="text"> <?php echo e(trans('site.phone')); ?></span>
                        </div>
                        <a href="tel:+<?php echo e($phone); ?>" class="item-val">
                            966<?php echo e($phone); ?>+
                        </a>
                    </div>
                    <div class="item">
                        <div class="d-flex text-icon">
                            <i class="fa-brands  fa-whatsapp icon"></i>
                            <span class="text"> <?php echo e(trans('site.whatsapp')); ?> </span>
                        </div>
                        <a href="#" class="item-val phone-val">
                            966<?php echo e($whatsapp); ?>+
                        </a>
                    </div>
                    <div class="item">
                        <div class="d-flex text-icon">
                            <i class="fa-regular icon  fa-envelope"></i>
                            <span class="text"> <?php echo e(trans('site.email')); ?> </span>
                        </div>
                        <a href="mailto:<?php echo e($email); ?>" class="item-val">
                            <?php echo e($email); ?>

                        </a>
                    </div>
                    
                </div>

                <div class="social-icons">
                    <?php $__currentLoopData = $social_media; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $media): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <a href="<?php echo e($media['link']); ?>">
                            <i class="fa-brands fa-<?php echo e($media['icon']); ?> icon"></i>
                        </a>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

                </div>
            </div>
        </div>
    </div>
</section>
<?php /**PATH D:\Workstation\hoomi\resources\views/site/includes/contact.blade.php ENDPATH**/ ?>