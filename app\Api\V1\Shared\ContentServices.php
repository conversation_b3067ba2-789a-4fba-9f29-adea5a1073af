<?php

namespace Tasawk\Api\V1\Shared;

use Tasawk\Api\Core;
use Tasawk\Api\Facade\Api;
use Tasawk\Http\Requests\Api\Customer\ContactUsRequest;
use Tasawk\Http\Resources\Api\Shared\PageResource;
use Tasawk\Http\Resources\Api\Shared\ReportReasonResource;
use Tasawk\Models\CancellationReason;
use Tasawk\Models\Content\Contact;
use Tasawk\Models\Content\Page;
use Tasawk\Models\Order\ReportReason;
use Tasawk\Settings\GeneralSettings;

class ContentServices {
    public function contact(ContactUsRequest $request) {
        Contact::create([...$request->validated(),'user_id'=>auth()?->guard('sanctum')?->id()]);
        return Api::isOk(__("Message sent successfully"));
    }

    public function page($page, GeneralSettings $settings) {
        $mapper = match ($page) {
            'about' => $settings->app_pages['about_us'],
            'terms' => $settings->app_pages['terms_and_conditions'],
            'privacy' => $settings->app_pages['privacy_policy'],
            default => null,
        };
        return Api::isOk(__("Page information"),PageResource::make(Page::findOrFail($mapper)));
    }
    public function reports(): Core {
        return Api::isOk('reports', ReportReasonResource::collection(ReportReason::enabled()
            ->when(request()->get('type') == 'customer', fn($builder) => $builder->customer())
            ->when(request()->get('type') == 'worker', fn($builder) => $builder->worker())
            ->when(!request()->filled('type') , fn($builder) => $builder->customer())
            ->get()));
    }
    public function cancels(): Core {
        return Api::isOk('reports', ReportReasonResource::collection(CancellationReason::enabled()->get()));
    }

}
