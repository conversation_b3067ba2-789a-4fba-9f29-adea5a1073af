<?php

namespace Tasawk\Http\Controllers;

use Illuminate\Http\Request;
use Tasawk\Models\Content\Banner;
use Tasawk\Settings\GeneralSettings;
use Tasawk\Settings\LandingPageSettings;
use Tasawk\Models\Content\CustomerReview;
use Tasawk\Models\Content\Page;
use Tasawk\Models\Faq;

class HomeController extends Controller
{
    public function index()
    {
        $generalSettings = new GeneralSettings();
        $settings = new LandingPageSettings();
        $about_description = $settings->about_description;
        $about_features = $settings->about_features;
        $our_features_description = $settings->our_features_description;
        $features = $settings->features;
        $feature_image = $settings->feature_image;
        $app_screens = $settings->app_screen;
        $apple_store_link = $generalSettings->applications_links['apple_store_link'] ?? '';
        $google_play_link = $generalSettings->applications_links['google_play_link'] ?? '';
        $logo_dark = $settings->logo_dark ?? '';
        $logo_light = $settings->logo_light ?? '';
        // $logo_dark_ar = $settings->logo_dark_ar ?? '';
        // $logo_light_ar = $settings->logo_light_ar ?? '';
        $phone = $generalSettings->app_phone;
        // $app_location = $generalSettings->app_location;
        $app_address = $generalSettings->app_address;
        $email = $generalSettings->app_email;
        $mobile = $generalSettings->app_mobile;
        $whatsapp = $generalSettings->app_whatsapp;
        $social_media = $generalSettings->social_links;
        $app_name = $generalSettings->app_name;
        $customer_reviews = CustomerReview::where('status', 1)->get();
        $faqs = Faq::where('status', 1)->take(5)->get();
        $banner = Banner::where(['status' => 1])->first();
        if (($banner) != null) {
            $banner_image = $banner->getFirstMediaUrl();
        } else {
            $banner_image = '';
        }
        return view(
            'site.index',
            compact(
                'about_description',
                'about_features',
                'mobile',
                'banner',
                'whatsapp',
                'email',
                'social_media',
                'app_name',
                'logo_light',
                'logo_dark',
                // 'logo_light_en',
                // 'logo_dark_en',
                'apple_store_link',
                'google_play_link',
                // 'app_location',
                'app_address',
                'phone',
                'our_features_description',
                'features',
                'feature_image',
                'app_screens',
                'customer_reviews',
                'faqs'
            )
        );
    }

    public function aboutUs()
    {
        $general_settings = new GeneralSettings();
        $about_us_id = $general_settings->app_pages['about_us'];
        $about_us = Page::findOrFail($about_us_id);
        $settings = new LandingPageSettings();
        $apple_store_link = $generalSettings->applications_links['apple_store_link'] ?? '';
        $google_play_link = $generalSettings->applications_links['google_play_link'] ?? '';
        $logo_dark = $settings->logo_dark ?? '';
        $logo_light = $settings->logo_light ?? '';
        $phone = $general_settings->app_phone;
        // $app_location = $general_settings->app_location;
        $app_address = $general_settings->app_address;
        $email = $general_settings->app_email;
        $mobile = $general_settings->app_mobile;
        $whatsapp = $general_settings->app_whatsapp;
        $social_media = $general_settings->social_links;
        $app_name = $general_settings->app_name;
        $banner = Banner::where(['status' => 1])->first();
        if (($banner) != null) {
            $banner_image = $banner->getFirstMediaUrl();
        } else {
            $banner_image = '';
        }
        return view('site.about-us', compact(
            'about_us',
            'apple_store_link',
            'google_play_link',
            'logo_light',
                'logo_dark',
            'phone',
            // 'app_location',
            'app_address',
            'email',
            'mobile',
            'whatsapp',
            'social_media',
            'banner',
            'app_name',
        ));
    }

    public function faqs()
    {
        $general_settings = new GeneralSettings();
        $faqs = Faq::where('status', 1)->take(5)->get();
        $settings = new LandingPageSettings();
        $apple_store_link = $generalSettings->applications_links['apple_store_link'] ?? '';
        $google_play_link = $generalSettings->applications_links['google_play_link'] ?? '';
        $logo_dark = $settings->logo_dark ?? '';
        $logo_light = $settings->logo_light ?? '';
        $phone = $general_settings->app_phone;
        // $app_location = $general_settings->app_location;
        $app_address = $general_settings->app_address;
        $email = $general_settings->app_email;
        $mobile = $general_settings->app_mobile;
        $whatsapp = $general_settings->app_whatsapp;
        $social_media = $general_settings->social_links;
        $app_name = $general_settings->app_name;
        $banner = Banner::where(['status' => 1])->first();
        if (($banner) != null) {
            $banner_image = $banner->getFirstMediaUrl();
        } else {
            $banner_image = '';
        }
        return view('site.faqs', compact(
            'faqs',
            'apple_store_link',
            'google_play_link',
            'logo_light',
                'logo_dark',
            'phone',
            // 'app_location',
            'app_address',
            'email',
            'mobile',
            'whatsapp',
            'social_media',
            'banner',
            'app_name',
        ));
    }

    public function term()
    {
        $general_settings = new GeneralSettings();
        $term_id = $general_settings->app_pages['terms_and_conditions'];
        $term = Page::findOrFail($term_id);
        $settings = new LandingPageSettings();
        $apple_store_link = $generalSettings->applications_links['apple_store_link'] ?? '';
        $google_play_link = $generalSettings->applications_links['google_play_link'] ?? '';
        $logo_dark = $settings->logo_dark ?? '';
        $logo_light = $settings->logo_light ?? '';
        $phone = $general_settings->app_phone;
        // $app_location = $general_settings->app_location;
        $app_address = $general_settings->app_address;
        $email = $general_settings->app_email;
        $mobile = $general_settings->app_mobile;
        $whatsapp = $general_settings->app_whatsapp;
        $social_media = $general_settings->social_links;
        $app_name = $general_settings->app_name;
        $banner = Banner::where(['status' => 1])->first();
        if (($banner) != null) {
            $banner_image = $banner->getFirstMediaUrl();
        } else {
            $banner_image = '';
        }
        return view('site.term', compact(
            'term',
            'apple_store_link',
            'google_play_link',
           'logo_light',
                'logo_dark',
            'phone',
            // 'app_location',
            'app_address',
            'email',
            'mobile',
            'whatsapp',
            'social_media',
            'banner',
            'app_name',
        ));
    }

    public function privacyPolicy()
    {
        $general_settings = new GeneralSettings();
        $privacy_policy_id = $general_settings->app_pages['privacy_policy'];
        $privacy_policy = Page::findOrFail($privacy_policy_id);
        $settings = new LandingPageSettings();
        $apple_store_link = $generalSettings->applications_links['apple_store_link'] ?? '';
        $google_play_link = $generalSettings->applications_links['google_play_link'] ?? '';
        $logo_dark = $settings->logo_dark ?? '';
        $logo_light = $settings->logo_light ?? '';
        $phone = $general_settings->app_phone;
        // $app_location = $general_settings->app_location;
        $app_address = $general_settings->app_address;
        $email = $general_settings->app_email;
        $mobile = $general_settings->app_mobile;
        $whatsapp = $general_settings->app_whatsapp;
        $social_media = $general_settings->social_links;
        $app_name = $general_settings->app_name;
        $banner = Banner::where(['status' => 1])->first();
        if (($banner) != null) {
            $banner_image = $banner->getFirstMediaUrl();
        } else {
            $banner_image = '';
        }
        return view('site.privacy-policy', compact(
            'privacy_policy',
            'apple_store_link',
            'google_play_link',
           'logo_light',
                'logo_dark',
            'phone',
            // 'app_location',
            'app_address',
            'email',
            'mobile',
            'whatsapp',
            'social_media',
            'banner',
            'app_name',
        ));
    }
}